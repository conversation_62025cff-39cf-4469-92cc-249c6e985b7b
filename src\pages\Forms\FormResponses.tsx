import { useEffect, useState, type FC, useMemo, useRef } from "react";
import { useUIStore } from "@/stores/uiStore";
import { File, Plus, Search, Settings2, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { FormResponseManagerSheet, FormResponsesFilterSheet } from "./sheets";
import { FormResponsesCard, FormResponsesHeader } from "./components";
import { useNavigate, useSearchParams } from "react-router";
import * as Types from "./types";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import {
	useGetFormResponses,
	useGetFormResponsesCounts,
	useDeleteFormResponse,
	useRejectFormResponse,
	useApproveFormResponse,
} from "./store/slices/formResponseSlice";
import { useDebounce } from "@/hooks/useDebounce";
import { Badge } from "@/components/ui/badge";
import { useModal } from "@/lib/hooks/useModal";
import { MarkTypeFormModal } from "./modals/MarkTypeFormModal";
import { format } from "date-fns";
import type { FormResponsesFilterSheetRef } from "./sheets/FormResponsesFilterSheet";
import { FormResponseApprovalModal } from "./modals/FormResponseApprovalModal";
import useCustomToast from "@/components/CustomToast";

// Map tab values to API type parameters
const getApiTypeParam = (tabValue: string): string | undefined => {
	if (tabValue === "all") return undefined; // Don't send type param for "all"
	if (tabValue === "general") return "general"; // API expects "general" not "general-inquiry"
	return tabValue; // "intake", "service", "feedback" map directly
};

// Filter data interface to match what FormsFilterSheet provides
interface FilterData {
	location_ids: string[];
	station_ids: string[];
	service_ids: string[];
	form_types: string[];
	form_statuses: string[];
	statuses: string[];
	flags: string[];
	client_ids: string[];
	date_from: string;
	date_to: string;
}

export const FormResponsesPage: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const customToast = useCustomToast();
	const [openFormResponseApprovalModal, setOpenFormResponseApprovalModal] =
		useState(false);
	const [formResponseNotes, setFormResponseNotes] = useState("");
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();
	const { openModal, closeModal, activeModal, updateModalData } = useModal();

	// Add ref for the filter sheet
	const filterSheetRef = useRef<FormResponsesFilterSheetRef>(null);
	// const formResponseManagerSheetRef =
	// 	useRef<FormResponseManagerSheetRef>(null);

	const [searchTerm, setSearchTerm] = useState("");
	const debouncedSearchTerm = useDebounce(searchTerm, 300);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [formResponseManagerControl, setFormResponseManagerControl] =
		useState<{
			open: boolean;
			formMode: "view" | "edit" | "manage" | null;
		}>({
			open: false,
			formMode: "manage",
		});

	const {
		mutate: approveFormResponse,
		isPending: isApproveFormResponsePending,
	} = useApproveFormResponse();

	const {
		mutate: rejectFormResponse,
		isPending: isRejectFormResponsePending,
	} = useRejectFormResponse();

	const [selectedForms, setSelectedForms] = useState<string[]>([]);
	const [showMarkTypeFormModal, setShowMarkTypeFormModal] = useState(false);
	const [selectedFormResponse, setSelectedFormResponse] =
		useState<Types.FormResponseTypes.FormResponseDataType | null>(null);

	const [currentPage, setCurrentPage] = useState(1);

	// Get current active tab from URL params
	const activeTab = searchParams.get("form-type-tab") || "all";
	const [appliedFilters, setAppliedFilters] = useState<FilterData>({
		location_ids: [],
		station_ids: [],
		service_ids: [],
		form_types: [],
		form_statuses: [],
		statuses: [],
		flags: [],
		client_ids: [],
		date_from: "",
		date_to: "",
	});

	// Transform filters to API parameters
	const apiParams = useMemo(() => {
		const params: Types.FormTypes.GetFormResponsesQueryParams = {
			page: currentPage.toString(),
			per_page: "10",
		};

		// Set type based on active tab
		const tabType = getApiTypeParam(activeTab);
		if (tabType) {
			params.statuses = tabType;
		}

		// Add search term if available
		if (debouncedSearchTerm) {
			params.search = debouncedSearchTerm;
		}

		// Force the query to include filter parameters even if empty
		// This ensures React Query sees parameter changes
		if (appliedFilters.location_ids.length > 0) {
			params.location_ids = appliedFilters.location_ids
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.station_ids.length > 0) {
			params.station_ids = appliedFilters.station_ids
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.service_ids.length > 0) {
			params.service_ids = appliedFilters.service_ids
				.map((id) => id.toString())
				.join(",");
		}

		// Only override form_statuses from applied filters if they exist
		// Otherwise keep the tab-based form_statuses
		if (appliedFilters.form_statuses.length > 0) {
			params.form_statuses = appliedFilters.form_statuses
				.map((id) => id.toString())
				.join(",");
		}

		// Add form_types parameter
		if (appliedFilters.form_types.length > 0) {
			params.form_types = appliedFilters.form_types
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.client_ids.length > 0) {
			params.client_ids = appliedFilters.client_ids
				.map((id) => id.toString())
				.map((id) => id.toString())
				.join(",");
		}

		if (appliedFilters.flags.length > 0) {
			params.flags = appliedFilters.flags
				.map((id) => id.toString())
				.map((id) => id.toString())
				.join(",");
		}

		// Add date parameters
		if (appliedFilters.date_from) {
			params.date_from = format(appliedFilters.date_from, "yyyy-MM-dd");
		}

		if (appliedFilters.date_to) {
			params.date_to = format(appliedFilters.date_to, "yyyy-MM-dd");
		}
		return params;
	}, [
		currentPage,
		activeTab,
		debouncedSearchTerm,
		appliedFilters.location_ids,
		appliedFilters.station_ids,
		appliedFilters.service_ids,
		appliedFilters.form_types,
		appliedFilters.form_statuses,
		appliedFilters.client_ids,
		appliedFilters.flags,
		appliedFilters.date_from,
		appliedFilters.date_to,
	]);

	// Get forms data for the active tab with applied filters
	const {
		data: formsData,
		isLoading,
		refetch: refetchForms,
	} = useGetFormResponses({
		params: apiParams,
	});

	const { mutate: deleteFormResponse, isPending: isDeletingForm } =
		useDeleteFormResponse();

	// Get counts for all form types with single optimized API call
	const { data: formResponseCounts } = useGetFormResponsesCounts();

	// Create dynamic tab items with counts
	const formTypeTabs = useMemo(
		() => [
			{
				value: "all",
				label: "All",
				count: formResponseCounts?.all || null,
			},
			{
				value: "pending",
				label: "Pending",
				count: formResponseCounts?.pending || null,
			},
			{
				value: "approved",
				label: "Approved",
				count: formResponseCounts?.approved || null,
			},
			{
				value: "declined",
				label: "Declined",
				count: formResponseCounts?.declined || null,
			},
			{
				value: "completed",
				label: "Completed",
				count: formResponseCounts?.completed || null,
			},
			{
				value: "blocked",
				label: "Blocked",
				count: formResponseCounts?.blocked || null,
			},
		],
		[formResponseCounts]
	);

	// Filter forms by search term on client-side (if API doesn't support search)
	const filteredForms = useMemo(() => {
		if (!formsData?.data) return [];

		let filtered = formsData.data;

		// Apply form type filter on client-side if multiple types are selected
		// or if we're not on the "all" tab and have additional form type filters
		if (
			appliedFilters.form_types.length > 0 &&
			(activeTab !== "all" || appliedFilters.form_types.length > 1)
		) {
			filtered = filtered.filter((form: any) =>
				appliedFilters.form_types.includes(form.type)
			);
		}

		// Apply search filter on client-side if API doesn't handle it
		if (debouncedSearchTerm && !apiParams.search) {
			filtered = filtered.filter(
				(form: any) =>
					form.name
						.toLowerCase()
						.includes(debouncedSearchTerm.toLowerCase()) ||
					form.description
						?.toLowerCase()
						.includes(debouncedSearchTerm.toLowerCase())
			);
		}

		return filtered;
	}, [formsData?.data, appliedFilters, debouncedSearchTerm, activeTab]);

	// Reset page when tab changes or filters are applied
	useEffect(() => {
		setCurrentPage(1);
	}, [activeTab, appliedFilters]);

	// Reset selected forms when tab changes
	useEffect(() => {
		setSelectedForms([]);
	}, [activeTab]);

	const handleSelectAll = (checked: boolean) => {
		if (checked && filteredForms) {
			setSelectedForms(filteredForms.map((form: any) => form.id));
		} else {
			setSelectedForms([]);
		}
	};

	const handleFormSelection = (formId: string, selected: boolean) => {
		if (selected) {
			setSelectedForms((prev) => [...prev, formId]);
		} else {
			setSelectedForms((prev) => prev.filter((id) => id !== formId));
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleViewForm = (
		form: Types.FormResponseTypes.FormResponseDataType
	) => {
		setFormResponseManagerControl({
			open: true,
			formMode: form.status !== "pending" ? "manage" : "view",
		});
		setSelectedFormResponse(form);
	};

	const handleViewMarkType = (
		form: Types.FormResponseTypes.FormResponseDataType
	) => {
		setSelectedFormResponse(form);
		setShowMarkTypeFormModal(true);
	};

	// Update modal loading state when isDeletingForm changes
	useEffect(() => {
		if (activeModal === "confirmation") {
			updateModalData({ isLoading: isDeletingForm });
		}
	}, [isDeletingForm, activeModal, updateModalData]);

	const handleDeleteForm = (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => {
		openModal("confirmation", {
			size: "md",
			data: {
				title: "Decline form?",
				message: `Are you sure you want to decline ${formResponse?.client?.name ? `${formResponse?.client?.name}’s` : ""} ${formResponse?.form?.name ? `submission of ${formResponse?.form?.name}` : ""} ${formResponse?.service?.name ? `for ${formResponse?.service?.name}` : ""} ${formResponse?.station?.name ? ` with ${formResponse?.station?.name}?` : "?"}`,
				confirmText: "Yes, Decline",
				cancelText: "No",
				variant: "destructive",
				onConfirm: () => {
					deleteFormResponse(
						{
							id: formResponse?.id,
						},
						{
							onSuccess: () => {
								closeModal();
								refetchForms();
							},
							onError: () => {
								// Optionally show an error toast
							},
						}
					);
				},
				onClose: () => {
					closeModal();
				},
			},
		});
	};

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Dashboard",
				href: "/",
			},
			{
				label: "Forms",
				href: "/dashboard/forms",
			},
			{
				label: "Form Manager",
				href: "/dashboard/forms",
			},
		]);

		setCurrentPageTitle("Form Manager");

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle]);

	const handleApplyFilters = (filterData: FilterData) => {
		setAppliedFilters(filterData);
		setShowFilterSheet(false);
		setCurrentPage(1); // Reset to first page when filters are applied
		// The query will automatically refetch due to the improved query key
	};

	// Clear filters function for future use
	const handleClearFilters = () => {
		// Call the handleReset method from the filter sheet component
		filterSheetRef.current?.handleReset();

		setAppliedFilters({
			location_ids: [],
			station_ids: [],
			service_ids: [],
			form_types: [],
			form_statuses: [],
			statuses: [],
			flags: [],
			client_ids: [],
			date_from: "",
			date_to: "",
		});
		setCurrentPage(1);
	};

	// Helper function to check if any filters are applied
	const hasActiveFilters = () => {
		return (
			appliedFilters.location_ids.length > 0 ||
			appliedFilters.station_ids.length > 0 ||
			appliedFilters.service_ids.length > 0 ||
			appliedFilters.form_types.length > 0 ||
			appliedFilters.form_statuses.length > 0 ||
			appliedFilters.client_ids.length > 0 ||
			appliedFilters.flags.length > 0 ||
			appliedFilters.date_from ||
			appliedFilters.date_to
		);
	};

	// Get count of active filters
	const activeFilterCount = () => {
		let count = 0;
		if (appliedFilters.location_ids.length > 0) count++;
		if (appliedFilters.station_ids.length > 0) count++;
		if (appliedFilters.service_ids.length > 0) count++;
		if (appliedFilters.form_types.length > 0) count++;
		if (appliedFilters.form_statuses.length > 0) count++;
		if (appliedFilters.client_ids.length > 0) count++;
		if (appliedFilters.flags.length > 0) count++;
		if (appliedFilters.date_from) count++;
		if (appliedFilters.date_to) count++;
		return count;
	};

	const handleRejectFormResponse = (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => {
		rejectFormResponse(
			{
				response_uuid: formResponse?.id || "",
				data: {
					notes: formResponseNotes,
				},
			},
			{
				onSuccess: (data) => {
					setFormResponseNotes("");
					setOpenFormResponseApprovalModal(false);
					customToast(
						data.message ||
							"Form response rejected successfully 🎉",
						{
							id: "reject-form-response",
							type: "success",
						}
					);
					refetchForms?.();
					setOpenFormResponseApprovalModal(false);
				},
				onError: (error: any) => {
					customToast(
						error.response.data.message ||
							"Failed to reject form response 🤕",
						{
							id: "reject-form-response",
							type: "error",
						}
					);
				},
			}
		);
	};

	const handleApproveFormResponse = (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => {
		approveFormResponse(
			{
				response_uuid: formResponse?.id || "",
				data: {
					notes: formResponseNotes,
				},
			},
			{
				onSuccess: (data) => {
					setFormResponseNotes("");
					setOpenFormResponseApprovalModal(false);
					customToast(
						data.message ||
							"Form response approved successfully 🎉",
						{
							id: "approve-form-response",
							type: "success",
						}
					);
					refetchForms?.();
					setOpenFormResponseApprovalModal(false);
				},
				onError: (error: any) => {
					customToast(
						error.response.data.message ||
							"Failed to approve form response 🤕",
						{
							id: "approve-form-response",
							type: "error",
						}
					);
				},
			}
		);
	};

	const handleSubmit = () => {
		if (selectedFormResponse?.status === "pending") {
			handleApproveFormResponse(
				selectedFormResponse as Types.FormResponseTypes.FormResponseDataType
			);
		} else {
			handleRejectFormResponse(
				selectedFormResponse as Types.FormResponseTypes.FormResponseDataType
			);
		}
	};

	// Common table content component to avoid duplication
	const TableContent = () =>
		isLoading ? (
			<div className="py-12 text-center">
				<div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
				<p className="mt-2 text-sm text-gray-500">Loading forms...</p>
			</div>
		) : filteredForms && filteredForms.length > 0 ? (
			<>
				<div className="grid w-full flex-1 overflow-hidden overflow-x-auto">
					<div className="grid min-w-[1182px] flex-col overflow-x-auto rounded-lg border border-zinc-200">
						<FormResponsesHeader
							selectedForms={selectedForms}
							formResponse={{
								data: filteredForms || [],
								meta: {
									pagination: {
										count:
											formsData?.meta.pagination.count ||
											0,
										current_page:
											formsData?.meta.pagination
												.current_page || 1,
										per_page:
											formsData?.meta.pagination
												.per_page || 10,
										total:
											formsData?.meta.pagination.total ||
											0,
										total_pages:
											formsData?.meta.pagination
												.total_pages || 0,
									},
								},
							}}
							handleSelectAll={handleSelectAll}
						/>

						{/* Forms Grid */}
						<div className="flex flex-col whitespace-nowrap">
							{filteredForms.map((formResponse: any) => (
								<FormResponsesCard
									key={formResponse.id}
									formResponse={formResponse}
									isSelected={selectedForms.includes(
										formResponse.id
									)}
									onSelectionChange={(selected: boolean) =>
										handleFormSelection(
											formResponse.id,
											selected
										)
									}
									onEdit={() =>
										console.log(
											"Edit form:",
											formResponse.id
										)
									}
									onView={() => handleViewForm(formResponse)}
									onViewMarkType={() =>
										handleViewMarkType(formResponse)
									}
									onDelete={() =>
										handleDeleteForm(formResponse)
									}
									onApprove={() => {
										setOpenFormResponseApprovalModal(true);
										setSelectedFormResponse(formResponse);
									}}
									onReject={() => {
										setOpenFormResponseApprovalModal(true);
										setSelectedFormResponse(formResponse);
									}}
								/>
							))}
						</div>
					</div>
				</div>
				{/* Pagination */}
				{formsData?.meta.pagination.total_pages &&
					formsData.meta.pagination.total_pages > 1 && (
						<div className="mt-8 flex items-center justify-center gap-2 p-4">
							<Button
								variant="outline"
								disabled={
									formsData?.meta.pagination.current_page ===
									1
								}
								onClick={() =>
									handlePageChange(
										formsData?.meta.pagination
											.current_page - 1
									)
								}
							>
								Previous
							</Button>

							<span className="text-sm text-gray-600">
								Page {formsData?.meta.pagination.current_page}{" "}
								of {formsData?.meta.pagination.total_pages}
							</span>

							<Button
								variant="outline"
								disabled={
									formsData?.meta.pagination.current_page ===
									formsData?.meta.pagination.total_pages
								}
								onClick={() =>
									handlePageChange(
										formsData?.meta.pagination
											.current_page + 1
									)
								}
							>
								Next
							</Button>
						</div>
					)}
			</>
		) : (
			<div className="space-y-6 py-12 text-center">
				<File className="mx-auto h-12 w-12" />
				<h3 className="mt-2 text-sm font-medium text-gray-900">
					No form responses added
				</h3>
				<p className="mt-1 text-sm text-gray-500">
					{searchTerm
						? `No form responses found matching "${searchTerm}"`
						: "Get started by creating your first form response."}
				</p>
			</div>
		);

	return (
		<div className="flex flex-col gap-4 py-6">
			{/* Header */}
			<div className="flex items-center justify-between pl-4">
				<h1 className="text-xl font-semibold">Form Responses</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search forms..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className={`relative h-10 cursor-pointer ${
							hasActiveFilters()
								? "border-primary bg-primary/10"
								: ""
						}`}
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="size-4" />
						{hasActiveFilters() && (
							<Badge
								variant="secondary"
								className="bg-primary absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center border-0 p-0 text-xs text-white"
							>
								{activeFilterCount()}
							</Badge>
						)}
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 h-10 cursor-pointer text-sm text-white hover:text-white"
						onClick={() => navigate("/dashboard/forms/create")}
					>
						<Plus className="mr-2 size-4" />
						Create a Form
					</Button>
				</div>
			</div>

			{/* Active Filters Indicator */}
			{hasActiveFilters() && (
				<div className="flex items-center gap-2 px-4">
					<span className="text-sm text-gray-600">
						Active filters:
					</span>
					<div className="flex flex-wrap items-center gap-2">
						{appliedFilters.location_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-blue-100 text-blue-800"
							>
								Locations ({appliedFilters.location_ids.length})
							</Badge>
						)}
						{appliedFilters.station_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-green-100 text-green-800"
							>
								Providers ({appliedFilters.station_ids.length})
							</Badge>
						)}
						{appliedFilters.service_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-purple-100 text-purple-800"
							>
								Services ({appliedFilters.service_ids.length})
							</Badge>
						)}
						{appliedFilters.form_statuses.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-orange-100 text-orange-800"
							>
								Form Statuses (
								{appliedFilters.form_statuses.length})
							</Badge>
						)}
						{appliedFilters.form_types.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-orange-100 text-orange-800"
							>
								Form Types ({appliedFilters.form_types.length})
							</Badge>
						)}
						{appliedFilters.client_ids.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-green-100 text-green-800"
							>
								Clients ({appliedFilters.client_ids.length})
							</Badge>
						)}
						{appliedFilters.flags.length > 0 && (
							<Badge
								variant="secondary"
								className="bg-yellow-100 text-yellow-800"
							>
								Flags ({appliedFilters.flags.length})
							</Badge>
						)}
						{appliedFilters.date_from && (
							<Badge
								variant="secondary"
								className="bg-blue-100 text-blue-800"
							>
								Date From:{" "}
								{format(appliedFilters.date_from, "MM/dd/yyyy")}
							</Badge>
						)}
						{appliedFilters.date_to && (
							<Badge
								variant="secondary"
								className="bg-purple-100 text-purple-800"
							>
								Date To:{" "}
								{format(appliedFilters.date_to, "MM/dd/yyyy")}
							</Badge>
						)}
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClearFilters}
							className="h-6 cursor-pointer px-2 text-xs text-gray-500 hover:text-gray-700"
						>
							<X className="mr-1 h-3 w-3" />
							Clear all
						</Button>
					</div>
				</div>
			)}

			{/* Tabs */}
			<Tabs
				items={formTypeTabs}
				defaultValue="all"
				useRouting={true}
				searchParamKey="form-type-tab"
			>
				<TabsContent value="all">
					<TableContent />
				</TabsContent>
				<TabsContent value="pending">
					<TableContent />
				</TabsContent>
				<TabsContent value="approved">
					<TableContent />
				</TabsContent>
				<TabsContent value="declined">
					<TableContent />
				</TabsContent>
				<TabsContent value="completed">
					<TableContent />
				</TabsContent>
				<TabsContent value="blocked">
					<TableContent />
				</TabsContent>
			</Tabs>

			{/* Filter Sheet */}
			<FormResponsesFilterSheet
				ref={filterSheetRef}
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
				isLoading={isLoading}
			/>

			{/* Form Response Manager Sheet */}
			<FormResponseManagerSheet
				formResponse={selectedFormResponse}
				sheetControl={formResponseManagerControl}
				onSheetControlChange={setFormResponseManagerControl}
				isLoading={isLoading}
				refetchForms={refetchForms}
			/>

			{/* Mark Type Form Modal */}
			<MarkTypeFormModal
				open={showMarkTypeFormModal}
				onOpenChange={setShowMarkTypeFormModal}
				response={selectedFormResponse || null}
				onSuccess={() => {
					setShowMarkTypeFormModal(false);
					refetchForms();
				}}
			/>

			{/* Form Response Approval Modal */}
			<FormResponseApprovalModal
				open={openFormResponseApprovalModal}
				onOpenChange={setOpenFormResponseApprovalModal}
				response={
					selectedFormResponse as Types.FormResponseTypes.FormResponseDataType
				}
				type={selectedFormResponse?.status as "approve" | "reject"}
				handleSubmit={handleSubmit}
				handleCancel={() => {
					setOpenFormResponseApprovalModal(false);
					setSelectedFormResponse(null);
					setFormResponseNotes("");
				}}
				formResponseNotes={formResponseNotes}
				handleNotesChange={(notes) => setFormResponseNotes(notes)}
				isLoading={
					isApproveFormResponsePending || isRejectFormResponsePending
				}
			/>
		</div>
	);
};
