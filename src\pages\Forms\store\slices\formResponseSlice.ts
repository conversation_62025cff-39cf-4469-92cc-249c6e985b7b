import { useMutation, useQuery } from "@tanstack/react-query";
import * as FormResponseTypes from "../../types/FormResponse";
import * as FormHttp from "../../http";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";

/**
 * Get Forms
 * @param params - Query parameters
 * @returns List of Forms
 */

export const useGetFormResponses = ({
	params,
}: {
	params: FormResponseTypes.GetFormResponsesQueryParams;
}) => {
	const { organization } = useOrganizationContext();
	// Create a stable query key that includes all parameter values
	// Filter out undefined values
	const filteredParams = Object.fromEntries(
		Object.entries(params).filter(([_, value]) => value !== undefined)
	) as Partial<FormResponseTypes.GetFormResponsesQueryParams>;

	// Create query key with only defined values
	const queryKey = ["forms-responses", ...Object.values(filteredParams)];

	return useQuery({
		queryKey,
		enabled: !!organization?.id,
		queryFn: () => {
			return FormHttp.APIVersion3GetFormResponses(
				params,
				organization?.id || 0
			);
		},
		// Enable refetch on mount to ensure fresh data
		refetchOnMount: true,
		// Keep data fresh
		staleTime: 0,
		// enabled: !!organizationId,
	});
};

/**
 * Get Form Counts for all types
 * @returns Form counts by type
 */
export const useGetFormResponsesCounts = () => {
	const { organization } = useOrganizationContext();

	return useQuery({
		queryKey: ["form-responses-counts", organization?.id],
		enabled: !!organization?.id,
		queryFn: async () => {
			// Make parallel requests for all form types
			const [
				allForms,
				pendingForms,
				approvedForms,
				declinedForms,
				completedForms,
				blockedForms,
			] = await Promise.all([
				FormHttp.APIVersion3GetFormResponses(
					{ page: "1", per_page: "1" },
					organization?.id || 0
				),
				FormHttp.APIVersion3GetFormResponses(
					{
						page: "1",
						per_page: "1",
						statuses: "pending",
					},
					organization?.id || 0
				),
				FormHttp.APIVersion3GetFormResponses(
					{
						page: "1",
						per_page: "1",
						statuses: "approved",
					},
					organization?.id || 0
				),
				FormHttp.APIVersion3GetFormResponses(
					{
						page: "1",
						per_page: "1",
						statuses: "declined",
					},
					organization?.id || 0
				),
				FormHttp.APIVersion3GetFormResponses(
					{
						page: "1",
						per_page: "1",
						statuses: "completed",
					},
					organization?.id || 0
				),
				FormHttp.APIVersion3GetFormResponses(
					{
						page: "1",
						per_page: "1",
						statuses: "blocked",
					},
					organization?.id || 0
				),
			]);

			return {
				all: allForms?.meta.pagination.total || 0,
				pending: pendingForms?.meta.pagination.total || 0,
				approved: approvedForms?.meta.pagination.total || 0,
				declined: declinedForms?.meta.pagination.total || 0,
				completed: completedForms?.meta.pagination.total || 0,
				blocked: blockedForms?.meta.pagination.total || 0,
			};
		},
		// enabled: !!organizationId,
	});
};

/**
 * Get Form Response
 * @param id - Form id
 * @returns Form
 */

export const useGetFormResponse = (id: string) => {
	const { organization } = useOrganizationContext();
	return useQuery({
		queryKey: ["form-response", id, organization?.id],
		// enabled: !!organization?.id,
		queryFn: () =>
			FormHttp.APIVersion3GetFormResponse(id, organization?.id || 0),
	});
};

/**
 * Create Form Response
 * @param data - Form data
 * @returns Form created response
 */

export const useCreateFormResponse = () => {
	const { organization } = useOrganizationContext();
	return useMutation({
		mutationFn: ({
			data,
		}: {
			data: FormResponseTypes.CreateFormResponses;
		}) => {
			return FormHttp.APIVersion3CreateFormResponses(
				data,
				organization?.id || 0
			);
		},
	});
};

/**
 * Update Form Response
 * @param id - Form id
 * @param data - Form data
 * @returns Form updated response
 */

export const useUpdateFormResponse = () => {
	const { organization } = useOrganizationContext();
	return useMutation({
		mutationFn: ({
			id,
			data,
		}: {
			id: string;
			data: FormResponseTypes.UpdateFormResponsePayload;
		}) =>
			FormHttp.APIVersion3UpdateFormResponse(
				id,
				data,
				organization?.id || 0
			),
	});
};

/**
 * Delete Form Response
 * @param id - Form id
 * @returns Form deleted response
 */

export const useDeleteFormResponse = () => {
	const { organization } = useOrganizationContext();
	return useMutation({
		mutationFn: ({ id }: { id: string }) =>
			FormHttp.APIVersion3DeleteFormResponse(id, organization?.id || 0),
	});
};

//************** For Form Responses Mark Type **************//

/**
 * Get Form Responses Mark Type
 * @returns Form responses mark type
 */

export const useGetFormResponsesMarkType = () => {
	const { organization } = useOrganizationContext();

	return useQuery({
		queryKey: ["form-responses-mark-type", organization?.id],
		enabled: !!organization?.id,
		queryFn: () =>
			FormHttp.APIVersion3GetFormResponsesMarkType(organization?.id || 0),
	});
};

/**
 * Mark Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response mark type
 * @returns Form response marked response
 */

export const useMarkFormResponse = () => {
	const { organization } = useOrganizationContext();
	return useMutation({
		mutationFn: ({
			response_uuid,
			data,
		}: {
			response_uuid: string;
			data: FormResponseTypes.MarkFormResponsePayload;
		}) =>
			FormHttp.APIVersion3MarkFormResponse(
				response_uuid,
				data,
				organization?.id || 0
			),
	});
};

/**
 * Approve Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response approval payload
 * @returns Form response approved response
 */

export const useApproveFormResponse = () => {
	const { organization } = useOrganizationContext();
	return useMutation({
		mutationFn: ({
			response_uuid,
			data,
		}: {
			response_uuid: string;
			data: FormResponseTypes.FormResponseApprovalPayload;
		}) =>
			FormHttp.APIVersion3ApproveFormResponse(
				response_uuid,
				data,
				organization?.id || 0
			),
	});
};

/**
 * Reject Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response approval payload
 * @returns Form response rejected response
 */

export const useRejectFormResponse = () => {
	const { organization } = useOrganizationContext();
	return useMutation({
		mutationFn: ({
			response_uuid,
			data,
		}: {
			response_uuid: string;
			data: FormResponseTypes.FormResponseApprovalPayload;
		}) =>
			FormHttp.APIVersion3RejectFormResponse(
				response_uuid,
				data,
				organization?.id || 0
			),
	});
};
