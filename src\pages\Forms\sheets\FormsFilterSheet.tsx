import { useState, useEffect } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import MultiAsyncSelect from "@/components/common/MultiAsyncSelect";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";
import { useServices } from "@/features/locations/hooks/useServices";
import { MultiSelectDropdown } from "@/components/common/MultiSelectDropdown";

interface FilterData {
	location_ids: string[];
	station_ids: string[];
	service_ids: string[];
	type: string[];
}

interface FilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters?: (filters: FilterData) => void;
	isLoading: boolean;
}

const FORM_TYPES = [
	{
		value: "all",
		label: "All",
	},
	{
		value: "intake",
		label: "Intake",
	},
	{
		value: "service",
		label: "Service",
	},
	{
		value: "general",
		label: "General Inquiry",
	},
	{
		value: "feedback",
		label: "Feedback",
	},
];

export const FormsFilterSheet = ({
	open,
	onOpenChange,
	onApplyFilters,
	isLoading,
}: FilterSheetProps) => {
	const { organizationId } = useOrganizationContext();

	const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"all",
	]);
	const [selectedFormTypes, setSelectedFormTypes] = useState<string[]>([
		"all",
	]);
	const [selectedServices, setSelectedServices] = useState<string[]>(["all"]);

	// Fetch real data from APIs
	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);

	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);

	const { data: servicesData, isLoading: isLoadingServices } = useServices({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
	});

	// Debug: Log loaded data
	// useEffect(() => {
	// 	console.log("🏢 Locations data:", locationsData);
	// 	console.log("🏪 Stations data:", stationsData);
	// 	console.log("🔧 Services data:", servicesData);
	// }, [locationsData, stationsData, servicesData]);

	const [filters, setFilters] = useState<FilterData>({
		location_ids: [],
		station_ids: [],
		service_ids: [],
		type: [],
	});

	const handleReset = () => {
		setSelectedLocations(["all"]);
		setSelectedProviders(["all"]);
		setSelectedServices(["all"]);
		setSelectedFormTypes(["all"]);
		setSelectedTypes([]);
		setFilters({
			location_ids: [],
			station_ids: [],
			service_ids: [],
			type: [],
		});
	};

	const handleApply = () => {
		// Transform selected values to actual data for filtering
		const appliedFilters: FilterData = {
			// Fix: If only "all" is selected, return empty array (no filter)
			// If "all" + others are selected, filter out "all" and keep the rest
			location_ids:
				selectedLocations.length === 1 &&
				selectedLocations.includes("all")
					? [] // Only "all" selected - no filter needed
					: selectedLocations.filter((id) => id !== "all"), // Remove "all" but keep specific locations
			station_ids:
				selectedProviders.length === 1 &&
				selectedProviders.includes("all")
					? [] // Only "all" selected - no filter needed
					: selectedProviders.filter((id) => id !== "all"), // Remove "all" but keep specific providers
			service_ids:
				selectedServices.length === 1 &&
				selectedServices.includes("all")
					? [] // Only "all" selected - no filter needed
					: selectedServices.filter((id) => id !== "all"), // Remove "all" but keep specific services
			// Fix: If only "all" is selected, return empty array
			// If "all" + others are selected, filter out "all" and keep the rest
			// If no "all" is selected, keep all selections
			type: selectedFormTypes.includes("all")
				? [] // Only "all" selected - no filter needed
				: selectedFormTypes.filter((type) => type !== "all"), // Remove "all" but keep specific types
		};

		// console.log("Filter sheet sending:", appliedFilters);
		// console.log("Selected locations:", selectedLocations);
		// console.log("Selected providers:", selectedProviders);
		// console.log("Selected services:", selectedServices);
		// console.log("Selected form types:", selectedFormTypes);

		onApplyFilters?.(appliedFilters);
		onOpenChange(false);
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	// Transform API data to options format
	const locationOptions = [
		{ value: "all", label: "All" },
		...(locationsData?.map((location) => ({
			value: location.id.toString(),
			label: location.name,
		})) || []),
	];

	const providerOptions = [
		{ value: "all", label: "All" },
		...(stationsData?.data?.map((station) => ({
			value: station.id.toString(),
			label: station.name,
		})) || []),
	];

	const serviceOptions = [
		{ value: "all", label: "All" },
		...(servicesData?.data?.map((service) => ({
			value: service.id.toString(),
			label: service.name,
		})) || []),
	];

	const formTypeOptions = [
		...FORM_TYPES.map((type) => ({
			value: type.value,
			label: type.label,
		})),
	];

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span className="text-2xl font-semibold">
							Filter Forms
						</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-[15px]">
						Select options below to help filter your search
					</p>
				</SheetHeader>

				<div className="space-y-6 overflow-y-auto px-0.5 py-6">
					{/* Locations Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Locations
						</label>
						<MultiSelectDropdown
							options={locationOptions}
							onValueChange={(values) => {
								setSelectedLocations(values);
							}}
							value={selectedLocations}
							placeholder={
								isLoadingLocations
									? "Loading locations..."
									: "Select locations"
							}
							className="w-full"
							disabled={isLoadingLocations}
						/>
					</div>

					{/* Providers Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Providers
						</label>
						<MultiSelectDropdown
							options={providerOptions}
							onValueChange={(values) => {
								setSelectedProviders(values);
							}}
							value={selectedProviders}
							placeholder={
								isLoadingStations
									? "Loading providers..."
									: "Select providers"
							}
							className="w-full"
							disabled={isLoadingStations}
						/>
					</div>

					{/* Services Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Services
						</label>
						<MultiSelectDropdown
							options={serviceOptions}
							onValueChange={(values) => {
								setSelectedServices(values);
							}}
							value={selectedServices}
							placeholder={
								isLoadingServices
									? "Loading services..."
									: "Select services"
							}
							className="w-full"
							disabled={isLoadingServices}
						/>
					</div>

					{/* Form Types Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Form Types
						</label>
						<MultiSelectDropdown
							options={formTypeOptions}
							onValueChange={(values) => {
								setSelectedFormTypes(values);
							}}
							value={selectedFormTypes}
							placeholder={"Select form types"}
							className="w-full"
							disabled={false}
							maxSelectedDisplay={13}
						/>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button variant="outline" onClick={handleCancel}>
							Cancel
						</Button>
						<Button onClick={handleApply}>Apply</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
};
