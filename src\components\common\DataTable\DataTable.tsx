import React from "react";
import { Checkbox } from "@/components/common/Checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export interface Column<T> {
	key: string;
	label: string;
	width?: "flex-1" | "flex-2" | "flex-3" | string;
	render?: (item: T) => React.ReactNode;
}

export interface DataTableProps<T> {
	columns: Column<T>[];
	data: T[];
	isLoading?: boolean;
	error?: Error | null;
	selectedItems?: string[];
	onSelectAll?: (checked: boolean) => void;
	onItemSelect?: (itemId: string, selected: boolean) => void;
	getItemId: (item: T) => string;
	renderItem: (item: T, isSelected: boolean, onSelect: (selected: boolean) => void) => React.ReactNode;
	emptyState?: {
		icon?: React.ReactNode;
		title: string;
		description: string;
		action?: {
			label: string;
			onClick: () => void;
		};
	};
	className?: string;
	loadingRowCount?: number;
}

export function DataTable<T>({
	columns,
	data,
	isLoading = false,
	error = null,
	selectedItems = [],
	onSelectAll,
	onItemSelect,
	getItemId,
	renderItem,
	emptyState,
	className = "",
	loadingRowCount = 5,
}: DataTableProps<T>) {
	const handleSelectAll = () => {
		if (onSelectAll) {
			const isAllSelected = data.length > 0 && selectedItems.length === data.length;
			onSelectAll(!isAllSelected);
		}
	};

	const handleItemSelect = (item: T) => {
		if (onItemSelect) {
			const itemId = getItemId(item);
			const isSelected = selectedItems.includes(itemId);
			onItemSelect(itemId, !isSelected);
		}
	};

	return (
		<div
			className={`flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200 ${className}`}
		>
			<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
				{onSelectAll && (
					<div className="flex items-center pr-4">
						<Checkbox
							label=""
							checked={
								data.length > 0 &&
								selectedItems.length === data.length
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
							disabled={isLoading}
						/>
					</div>
				)}
				{columns.map((column) => (
					<div
						key={column.key}
						className={`flex items-center px-3 text-[#71717A] ${
							column.width || "flex-1"
						}`}
					>
						<div className="flex items-center gap-3">
							<p>{column.label}</p>
						</div>
					</div>
				))}
			</div>

			{isLoading ? (
				<div className="flex flex-col gap-0.5">
					{Array.from({ length: loadingRowCount }).map((_, index) => (
						<div
							key={index}
							className="flex h-16 items-center justify-between border-b border-gray-100 py-2 pl-4"
						>
							{onSelectAll && (
								<div className="flex items-center pr-4">
									<Skeleton className="h-4 w-4" />
								</div>
							)}
							{columns.map((column) => (
								<div
									key={column.key}
									className={`flex items-center px-3 ${
										column.width || "flex-1"
									}`}
								>
									<Skeleton className="h-4 w-20" />
								</div>
							))}
						</div>
					))}
				</div>
			) : error ? (
				<div className="py-12 text-center">
					<div className="text-red-500">
						Error loading data: {error.message}
					</div>
				</div>
			) : data.length === 0 ? (
				<div className="py-12 text-center">
					{emptyState?.icon && (
						<div className="mx-auto h-12 w-12 text-gray-400">
							{emptyState.icon}
						</div>
					)}
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						{emptyState?.title || "No data found"}
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						{emptyState?.description || "No items to display."}
					</p>
					{emptyState?.action && (
						<Button
							className="mt-4"
							onClick={emptyState.action.onClick}
						>
							<Plus className="mr-2 h-4 w-4" />
							{emptyState.action.label}
						</Button>
					)}
				</div>
			) : (
				<div className="flex flex-col gap-0.5">
					{data.map((item) => {
						const itemId = getItemId(item);
						const isSelected = selectedItems.includes(itemId);
						return renderItem(item, isSelected, (selected) =>
							handleItemSelect(item)
						);
					})}
				</div>
			)}
		</div>
	);
}
