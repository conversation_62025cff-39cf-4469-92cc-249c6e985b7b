import { Controller } from "react-hook-form";
import { Uploader } from "@/components/common/Uploader";
import { ChevronDown, Upload } from "lucide-react";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { FormTypes } from "../types";

export const EnhancedFieldRenderer: React.FC<{
	control: any;
	mode: string;
	errors: any;
	field: FormTypes.FormField;
}> = ({ field, control, mode, errors }) => {
	const isReadOnly = mode === "view";
	const fieldError = errors[field.id];

	const formatMap: Record<string, string> = {
		"Word Doc": ".doc,.docx",
		PNG: ".png",
		PDF: ".pdf",
		CSV: ".csv",
		JPEG: ".jpeg",
	};

	const getAcceptedFormats = (field: FormTypes.FormField) => {
		if (!field.approved_formats || field.approved_formats.length === 0)
			return "";
		return field.approved_formats
			.map((format) => formatMap[format] || `.${format.toLowerCase()}`)
			.join(",");
	};

	return (
		<>
			{field?.type === "info_text" ? (
				<div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
					<div className="flex items-start gap-3">
						<div className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full bg-blue-600">
							<span className="text-xs text-white">i</span>
						</div>
						<div>
							<h4 className="font-medium text-blue-900">
								{field.title}
							</h4>
							<p className="mt-1 text-blue-800">
								{field.info_text_value}
							</p>
						</div>
					</div>
				</div>
			) : field?.type === "info_image" ? (
				<div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
					<div className="mb-3 flex items-center gap-3">
						<div className="h-5 w-5 rounded bg-gray-600"></div>
						<h4 className="font-medium text-gray-900">
							{field.title}
						</h4>
					</div>
					{field.description && (
						<p className="mb-3 text-gray-600">
							{field.description}
						</p>
					)}
					{field.image && (
						<div className="mx-auto flex max-h-[400px] max-w-[480px] items-center gap-2">
							<img
								src={field?.image || ""}
								alt={field.title}
								className="h-full w-full object-cover"
							/>
						</div>
					)}
				</div>
			) : (
				<div className="space-y-4">
					<label className="block text-lg font-medium">
						{field.title}
						{field.required && (
							<span className="ml-1 text-red-500">*</span>
						)}
					</label>

					{field.description && (
						<p className="text-sm text-gray-500">
							{field.description}
						</p>
					)}

					<Controller
						name={field.id}
						control={control}
						render={({ field: controllerField }) => {
							const baseInputClass = `w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
								isReadOnly
									? "bg-gray-50 cursor-not-allowed"
									: "bg-white"
							} ${fieldError ? "border-red-500" : "border-gray-300"}`;

							switch (field.type) {
								case "text":
								case "email":
								case "phone":
									return (
										<input
											type={
												field.type === "email"
													? "email"
													: field.type === "phone"
														? "tel"
														: "text"
											}
											{...controllerField}
											placeholder={
												field.placeholder || ""
											}
											disabled={isReadOnly}
											className={baseInputClass}
										/>
									);

								case "long_text":
									return (
										<textarea
											{...controllerField}
											placeholder={
												field.placeholder || ""
											}
											disabled={isReadOnly}
											rows={4}
											className={`${baseInputClass} resize-vertical`}
										/>
									);

								case "numeric":
								case "number":
								case "scale_1_10":
									return (
										<input
											type="number"
											{...controllerField}
											onChange={(e) =>
												controllerField.onChange(
													Number(e.target.value)
												)
											}
											placeholder={
												field.placeholder || ""
											}
											disabled={isReadOnly}
											className={baseInputClass}
										/>
									);

								case "date":
									return (
										<DatePicker
											{...controllerField}
											variant="default"
											value={
												mode === "edit"
													? controllerField.value
													: undefined
											}
											onChange={controllerField.onChange}
											disabled={isReadOnly}
											className="h-9 w-full"
										/>
									);

								case "date_range":
									return (
										// <div className="grid grid-cols-2 gap-4">
										// 	<div>
										// 		<label className="mb-1 block text-xs text-gray-500">
										// 			Start Date
										// 		</label>
										// 		<input
										// 			type="date"
										// 			value={
										// 				controllerField.value
										// 					?.start_date || ""
										// 			}
										// 			onChange={(e) =>
										// 				controllerField.onChange({
										// 					...controllerField.value,
										// 					start_date: e.target.value,
										// 				})
										// 			}
										// 			disabled={isReadOnly}
										// 			className={baseInputClass}
										// 		/>
										// 	</div>
										// 	<div>
										// 		<label className="mb-1 block text-xs text-gray-500">
										// 			End Date
										// 		</label>
										// 		<input
										// 			type="date"
										// 			value={
										// 				controllerField.value
										// 					?.end_date || ""
										// 			}
										// 			onChange={(e) =>
										// 				controllerField.onChange({
										// 					...controllerField.value,
										// 					end_date: e.target.value,
										// 				})
										// 			}
										// 			disabled={isReadOnly}
										// 			className={baseInputClass}
										// 		/>
										// 	</div>
										// </div>

										<div className="flex w-full items-center gap-2">
											<DatePicker
												{...controllerField}
												variant="default"
												value={
													mode === "edit"
														? controllerField.value
														: undefined
												}
												onChange={(value: any) =>
													controllerField.onChange({
														...controllerField.value,
														start_date: value,
													})
												}
												disabled={isReadOnly}
												className="h-9 w-auto flex-1"
											/>
											<span className="text-xs">To</span>
											<DatePicker
												{...controllerField}
												variant="default"
												value={
													mode === "edit"
														? controllerField.value
														: undefined
												}
												onChange={(value: any) =>
													controllerField.onChange({
														...controllerField.value,
														end_date: value,
													})
												}
												disabled={isReadOnly}
												className="h-9 w-auto flex-1"
											/>
										</div>
									);

								case "dropdown":
									return (
										<div className="relative">
											<select
												className="w-full appearance-none rounded border px-3 py-2.5 text-sm font-normal text-[#18181B]"
												{...controllerField}
												disabled={isReadOnly}
											>
												<option value="">
													Select an option
												</option>
												{field?.options?.map(
													(option: any) => (
														<option
															key={option.id}
															value={option.value}
														>
															{option.value}
														</option>
													)
												)}
											</select>
											<ChevronDown className="absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2" />
										</div>
									);

								case "radio":
								case "yes_no":
								case "boolean":
									// case "satisfaction_scale":
									// case "agree_disagree":
									// case "rating":
									return (
										<div className="space-y-2">
											{field?.options &&
											field?.options?.length > 0
												? field?.options?.map(
														(option: any) => (
															<label
																key={option.id}
																className="flex cursor-pointer items-center space-x-2"
															>
																<input
																	type="radio"
																	value={
																		option.value
																	}
																	checked={
																		controllerField.value ===
																		option.value
																	}
																	onChange={() =>
																		controllerField.onChange(
																			option.value
																		)
																	}
																	disabled={
																		isReadOnly
																	}
																	className="text-primary focus:ring-primary/90"
																/>
																<span className="text-sm text-gray-700">
																	{
																		option.value
																	}
																</span>
															</label>
														)
													)
												: [
														{
															id: "true",
															value: "Yes",
														},
														{
															id: "false",
															value: "No",
														},
													].map((option) => (
														<label
															key={option.id}
															className="flex cursor-pointer items-center space-x-2"
														>
															<input
																type="radio"
																value={
																	option.value
																}
																checked={
																	controllerField.value ===
																	option.value
																}
																onChange={() =>
																	controllerField.onChange(
																		option.value
																	)
																}
																disabled={
																	isReadOnly
																}
																className="text-primary focus:ring-primary/90"
															/>
															<span className="text-sm text-gray-700">
																{option.value}
															</span>
														</label>
													))}
										</div>
									);

								case "checkbox":
									return (
										<div className="space-y-2">
											{field?.options?.map(
												(option: any) => (
													<label
														key={option.id}
														className="flex cursor-pointer items-center space-x-2"
													>
														<input
															type="checkbox"
															checked={
																controllerField.value?.includes(
																	option.value
																) || false
															}
															onChange={(e) => {
																const currentValues =
																	controllerField.value ||
																	[];
																if (
																	e.target
																		.checked
																) {
																	controllerField.onChange(
																		[
																			...currentValues,
																			option.value,
																		]
																	);
																} else {
																	controllerField.onChange(
																		currentValues.filter(
																			(
																				v: string
																			) =>
																				v !==
																				option.value
																		)
																	);
																}
															}}
															disabled={
																isReadOnly
															}
															className="text-primary focus:ring-primary"
														/>
														<span className="text-sm font-medium text-[#27272A]">
															{option.value}
														</span>
													</label>
												)
											)}
											{/* {field.selection_limit && (
										<p className="text-xs text-gray-500">
											Maximum {field.selection_limit}{" "}
											selections allowed
										</p>
									)} */}
										</div>
									);

								case "yes_no":
									return (
										<label className="flex cursor-pointer items-center space-x-2">
											<input
												type="checkbox"
												checked={
													controllerField.value ||
													false
												}
												onChange={(e) =>
													controllerField.onChange(
														e.target.checked
													)
												}
												disabled={isReadOnly}
												className="rounded text-blue-600 focus:ring-blue-500"
											/>
											<span className="text-sm text-gray-700">
												Yes
											</span>
										</label>
									);

								case "attachment":
									return (
										<div className="mx-auto max-w-[480px]">
											<Uploader
												files={
													controllerField.value || []
												}
												// onFilesChange={() => {}}
												onFileRemove={() => {
													controllerField?.onChange(
														""
													);
												}}
												descriptionText={`Recommended file type: ${getAcceptedFormats(
													field
												)} (Max of 10 mb)`}
												accept={getAcceptedFormats(
													field
												)}
												maxFileSize={10 * 1024 * 1024}
												multiple={false}
												maxFiles={1}
												size="sm"
												uploadText="Click or drag file here to upload file"
												uploadIcon={
													<Upload className="h-4 w-4 text-black" />
												}
												enableServerUpload={true}
												onUploadSuccess={(
													_,
													url: string
												) => {
													controllerField?.onChange(
														url
													);
												}}
											/>
										</div>
									);

								default:
									return (
										<div className="text-sm text-gray-500 italic">
											Unsupported field type: {field.type}
										</div>
									);
							}
						}}
					/>

					{fieldError && (
						// <Alert className="border-red-200 bg-red-50">
						// 	<AlertCircle className="h-4 w-4 text-red-600" />
						// 	<AlertDescription className="text-red-700">
						// 		{fieldError.message}
						// 	</AlertDescription>
						// </Alert>
						<p className="text-red-700">{fieldError.message}</p>
					)}
				</div>
			)}
		</>
	);
};
