import React, { useState } from "react";
import { Plus, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { DataTable, type Column } from "./DataTable/DataTable";
import { IntakeFieldCard } from "./IntakeFieldCard";
import { AddIntakeFieldSheet } from "@/components/sheets/AddIntakeFieldSheet";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	useCustomIntakes,
	useUpdateFieldRequirement,
	useUpdateFieldVisibility,
	useDeleteCustomIntake,
} from "@/hooks/useCustomIntakes";
import { type IntakeField } from "@/lib/api/customIntakesApi";

export interface IntakeFieldsResponse {
	success: boolean;
	data: {
		intakes: IntakeField[];
		meta: Record<string, any>;
	};
	message: string;
}

interface IntakeFieldsTableProps {
	className?: string;
	selectedFields?: string[];
	onSelectAll?: (checked: boolean) => void;
	onFieldSelect?: (fieldId: string, selected: boolean) => void;
}

export function IntakeFieldsTable({
	className = "",
	selectedFields = [],
	onSelectAll,
	onFieldSelect,
}: IntakeFieldsTableProps) {
	const { organizationId } = useOrganizationContext();
	const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
		{}
	);
	const [isAddFieldSheetOpen, setIsAddFieldSheetOpen] = useState(false);

	// Fetch custom intakes data
	const {
		data: customIntakesResponse,
		isLoading,
		error,
	} = useCustomIntakes({
		organizationId: organizationId?.toString() || "",
		limit: 50, // Fetch more items to show all fields
	});

	// Mutation hooks
	const updateRequirementMutation = useUpdateFieldRequirement();
	const updateVisibilityMutation = useUpdateFieldVisibility();
	const deleteIntakeMutation = useDeleteCustomIntake();

	// Extract fields from response
	const fields = customIntakesResponse?.data?.intakes || [];

	const handleEdit = (field: IntakeField) => {
		// TODO: Implement edit functionality
		console.log("Edit field:", field);
	};

	const handleDelete = async (field: IntakeField) => {
		if (!organizationId) return;

		setLoadingStates((prev) => ({ ...prev, [`delete-${field.id}`]: true }));
		try {
			await deleteIntakeMutation.mutateAsync({
				id: field.id,
				organizationId: organizationId.toString(),
			});
		} finally {
			setLoadingStates((prev) => ({
				...prev,
				[`delete-${field.id}`]: false,
			}));
		}
	};

	const handleToggleRequired = async (
		fieldId: number,
		required: "yes" | "no" | "optional"
	) => {
		if (!organizationId) return;

		setLoadingStates((prev) => ({
			...prev,
			[`required-${fieldId}`]: true,
		}));
		try {
			await updateRequirementMutation.mutateAsync({
				id: fieldId,
				requirement: required,
				organizationId: organizationId.toString(),
			});
		} finally {
			setLoadingStates((prev) => ({
				...prev,
				[`required-${fieldId}`]: false,
			}));
		}
	};

	const handleToggleActive = async (fieldId: number, active: boolean) => {
		if (!organizationId) return;

		setLoadingStates((prev) => ({ ...prev, [`active-${fieldId}`]: true }));
		try {
			await updateVisibilityMutation.mutateAsync({
				id: fieldId,
				isVisible: active,
				organizationId: organizationId.toString(),
			});
		} finally {
			setLoadingStates((prev) => ({
				...prev,
				[`active-${fieldId}`]: false,
			}));
		}
	};

	// Define columns for the DataTable
	const columns: Column<IntakeField>[] = [
		{
			key: "name",
			label: "Field Name",
			width: "flex-1",
		},
		{
			key: "type",
			label: "Type",
			width: "w-24",
		},
		{
			key: "apply_to",
			label: "Station",
			width: "w-24",
		},
		{
			key: "field_requirement",
			label: "List",
			width: "w-24",
		},
		{
			key: "actions",
			label: "Actions",
			width: "w-20",
		},
	];

	// Filter fields by category
	const locationFields = fields.filter(
		(field) => field.apply_to === "location" || field.apply_to === "all"
	);
	const otherFields = fields.filter(
		(field) => field.apply_to !== "location" && field.apply_to !== "all"
	);

	const renderTable = (
		fieldsData: IntakeField[],
		emptyTitle: string,
		emptyDescription: string
	) => (
		<DataTable
			columns={columns}
			data={fieldsData}
			isLoading={isLoading}
			selectedItems={selectedFields}
			onSelectAll={onSelectAll}
			onItemSelect={onFieldSelect}
			getItemId={(field) => field.id.toString()}
			renderItem={(field, isSelected, onSelect) => (
				<IntakeFieldCard
					key={field.id}
					field={field}
					isSelected={isSelected}
					onSelectionChange={onSelect}
					onEdit={handleEdit}
					onDelete={handleDelete}
					onToggleRequired={handleToggleRequired}
					onToggleActive={handleToggleActive}
					loadingStates={loadingStates}
				/>
			)}
			emptyState={{
				icon: <FileText className="mx-auto h-12 w-12 text-gray-400" />,
				title: emptyTitle,
				description: emptyDescription,
				action: isAddFieldSheetOpen
					? undefined
					: {
							label: "Add Intake Field",
							onClick: () => setIsAddFieldSheetOpen(true),
						},
			}}
		/>
	);

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-sm text-gray-500">
					Loading intake fields...
				</div>
			</div>
		);
	}

	return (
		<div className={`space-y-4 ${className}`}>
			{/* Header with Add Button */}
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-semibold">Intake Fields</h3>
				<div className="flex items-center gap-2">
					<Button variant="outline" className="text-sm">
						Preview Form
					</Button>
					<Button
						onClick={() => setIsAddFieldSheetOpen(true)}
						className="bg-primary hover:bg-primary/90 text-white"
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Intake Field
					</Button>
				</div>
			</div>

			{/* Tabs for different intake field categories */}
			<Tabs defaultValue="location" className="w-full">
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger value="location">
						Location Intake Fields ({locationFields.length})
					</TabsTrigger>
					<TabsTrigger value="other">
						All Other Intake Fields ({otherFields.length})
					</TabsTrigger>
				</TabsList>

				<TabsContent value="location" className="mt-4">
					{renderTable(
						locationFields,
						"No location intake fields found",
						"Add your first location intake field to get started"
					)}
				</TabsContent>

				<TabsContent value="other" className="mt-4">
					{renderTable(
						otherFields,
						"No intake fields found",
						"Add your first intake field to get started"
					)}
				</TabsContent>
			</Tabs>

			{/* Add Intake Field Sheet */}
			<AddIntakeFieldSheet
				open={isAddFieldSheetOpen}
				onOpenChange={setIsAddFieldSheetOpen}
			/>
		</div>
	);
}
