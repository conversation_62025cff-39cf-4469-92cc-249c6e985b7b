import * as Types from "../types";

export type GetFormResponsesQueryParams = {
	location_ids?: string;
	station_ids?: string;
	service_ids?: string;
	form_types?: string;
	form_statuses?: string;
	statuses?: string;
	flags?: string;
	client_ids?: string;
	date_from?: string;
	date_to?: string;
	page?: string;
	per_page?: string;
	search?: string;
};

export type GetFormResponsesResponse = {
	success: boolean;
	message: string;
	data: any[];
	meta: {
		pagination: {
			count: number;
			current_page: number;
			per_page: number;
			total: number;
			total_pages: number;
		};
	};
};

export type Responses = {
	field_id: string;
	value: string;
	value_type: string;
};

export type CreateFormResponses = {
	form_id: string;
	session_id: string;
	status: string;
	responses: Responses[];
	customer_id: string;
};

export type CreateFormResponsesResponse = {
	success: boolean;
	message: string;
	data: any;
};

export type FormData = {
	id: string;
	name: string;
	version: number;
	status: string;
	type_label: string;
	type: string;
};

export type ClientData = {
	id: number;
	name: string;
	first_name: string;
	last_name: string;
	email: string;
	phone_number: string;
};

export type ServiceData = {
	id: number;
	name: string;
};

export type StationData = {
	id: number;
	name: string;
	description: string | null;
};

export type LocationData = {
	id: number;
	name: string;
	address: string;
	city: string;
	state: string;
	country: string;
};

export type FormResponseDataType = {
	id: string;
	client: ClientData | null;
	service: ServiceData | null;
	location: LocationData | null;
	station: StationData | null;
	customer_id: number | null;
	station_id: string | null;
	location_id: string | null;
	service_id: string | null;
	status: string;
	block_reason: string | null;
	submitted_at: string;
	mark_type: null;
	mark_label: null;
	mark_color: null;
	marking_notes: null;
	marked_at: null;
	approval_status: null;
	approval_notes: null;
	approved_at: null;
	created_at: string;
	updated_at: string;
	form: FormData | null;
	responses: FormResponsesData;
};

export type FormResponseResponseData = {
	success: boolean;
	message: string;
	data: FormResponseDataType;
};

export type FormFieldResponse = {
	id: string;
	form_response_id: string;
	field_id: string;
	value: any;
	value_type: string;
	created_at: string;
	updated_at: string;
};

export type Customer = {
	id: string;
	name: string;
};

// API Response Type (what you get from the server)
export type FormResponse = {
	banner_url: string | null;
	block_message: string | null;
	collect_general_feedback: boolean | null;
	collect_rating: boolean | null;
	created_at: string;
	general_feedback_title: string | null;
	id: string;
	is_auto_approve: boolean | null;
	location_id: string | null;
	location_ids: string[];
	locations: LocationData[];
	logo_url: null;
	name: string;
	service_id: string | null;
	service_ids: string[];
	services: ServiceData[];
	station_id: string | null;
	station_ids: string[];
	stations: StationData[];
	status: string;
	submit_button_title: string;
	success_message: string;
	type: string;
	type_label: string;
	updated_at: string;
	validation_fields: null;
	version: number;
};

type FormSection = {
	section_id: string;
	section_title: string;
	section_description: string;
	order: number;
	fields: FormField[];
};

type FormField = {
	field_id: string;
	field_title: string;
	field_type: FieldType;
	field_description: string | null;
	field_placeholder: string | null;
	required: boolean;
	order: number;
	raw_value: FieldValue;
	display_value: FieldValue;
	value_type: ValueType;
};

type FieldType =
	| "checkbox"
	| "satisfaction_scale"
	| "long_text"
	| "text"
	| "email"
	| "radio"
	| "numeric"
	| "date"
	| "time"
	| "datetime"
	| "yes_no";

type ValueType = "array" | "option_uuid" | "long_text" | "text" | "email";

export type FieldValue = string | string[] | null;

export type FormResponsesData = FormSection[];

// Update Payload Type (what you send to update)
export type UpdateFormResponsePayload = {
	responses: Array<{
		field_id: string;
		value: any;
	}>;
	user_id?: number;
};

//**************************** Form Responses Mark Type API ****************************//

export type GetFormResponsesMarkTypeQueryParams = {
	location_ids: string[];
	station_ids: string[];
	service_ids: string[];
	form_types: string[];
	form_statuses: string[];
	flags: string[];
	client_ids: string[];
	date_from: string;
	date_to: string;
};

export type FormResponseMarkType = {
	value: string;
	label: string;
	color: string;
};

export type GetFormResponsesMarkTypeResponse = {
	success: boolean;
	message: string;
	data: FormResponseMarkType[];
};

export type MarkFormResponsePayload = {
	mark_type: string;
	notes: string;
};

export type MarkFormResponseResponseData = {
	id: string;
	status: string;
	mark_type: string | null;
	mark_label: string | null;
	mark_color: string | null;
	marking_notes: string | null;
	marked_at: string | null;
	approval_status: string;
	created_at: string;
};

export type FormResponseApprovalResponseData = {
	id: string;
	status: string;
	mark_type: string;
	approval_status: string;
	approval_notes: string;
	approved_at: string;
	created_at: string;
};

export type CreateMarkFormResponseResponse = {
	success: boolean;
	message: string;
	data: MarkFormResponseResponseData;
};

export type FormResponseApprovalResponse = {
	success: boolean;
	message: string;
	data: FormResponseApprovalResponseData;
};

export type FormResponseApprovalPayload = {
	notes: string;
};
