import { createBrowserRouter } from "react-router";
import { MainLayout } from "../layouts/MainLayout/MainLayout";
import Dashboard from "../pages/Dashboard/Dashboard";
import DashboardTest from "../pages/Dashboard/DashboardTest";
import { Locations } from "@/pages/WorkPlace";
import {
	SignInPage,
	ForgotPasswordPage,
	ResetPasswordPage,
	MFAPage,
	AuthCallbackPage,
} from "../pages/Auth";
import { ManagementTabs } from "@/pages/WorkPlace/ManagementTabs";
import SchedulePage from "@/pages/schedules";
import LocationProviders from "@/pages/WorkPlace/LocationProviders";
import LocationProviderDetails from "@/pages/WorkPlace/LocationProviderDetails";
import Clients from "@/pages/ManageClients/Clients";
import ImportCSV from "@/pages/ManageClients/ImportCSV";
import { PlannerApp } from "@/pages/Planner";
import PatientCategories from "@/pages/ManageClients/PatientCategories";
import PatientReviews from "@/pages/ManageClients/PatientReviews";
import { AllFormsPage, FormResponsesPage } from "@/pages/Forms";
import { CreateFormPage } from "@/pages/Forms/CreateFormPage";
import Settings from "@/pages/settings/settings";
import ScheduleHistory from "@/pages/schedules/ScheduleHistory";
import Automation from "@/pages/automation";
import CreateAutomation from "@/pages/automation/editor/create";
import { FormPreviewPage } from "@/pages/Forms/FormPreview";
import AutomationHistory from "@/pages/automation/history";
import Analytics from "@/pages/Analytics";
import CreateAutomationEditor from "@/pages/automation/editor/create";

export const router = createBrowserRouter([
	{
		path: "/",
		Component: MainLayout,
		children: [
			// Home/landing page
			{
				index: true,
				Component: Dashboard,
			},

			// Dashboard section
			{
				path: "dashboard",
				children: [
					{
						index: true,
						Component: Dashboard,
					},
					{
						path: "test",
						Component: DashboardTest,
					},
					{
						path: "analytics",
						Component: Analytics,
					},
					{
						path: "workplace",
						children: [
							{
								index: true,
								Component: Locations,
							},
							{
								path: "locations",
								Component: Locations,
							},
							{
								path: "providers/:providerId",
								Component: LocationProviders,
							},
							{
								path: "providers/provider-details/:providerId",
								Component: LocationProviderDetails,
							},
						],
					},
					{
						path: "schedule",
						children: [
							{
								path: "manage-appointments",
								Component: SchedulePage,
							},
							{
								path: "history",
								Component: ScheduleHistory,
							},
							{
								path: "planner/*",
								Component: PlannerApp,
							},
						],
					},
					{
						path: "planner/*",
						Component: PlannerApp,
					},
					{
						path: "patients",
						children: [
							{
								index: true,
								Component: Clients,
							},
							{
								path: "import-csv",
								Component: ImportCSV,
							},
							{
								path: "categories",
								Component: PatientCategories,
							},
							{
								path: "reviews",
								Component: PatientReviews,
							},
						],
					},
					{
						path: "forms",
						// Component: AllFormsPage,
						children: [
							{
								index: true,
								Component: AllFormsPage,
							},
							{
								path: "responses",
								Component: FormResponsesPage,
							},
							{
								path: "create",
								Component: CreateFormPage,
							},
							{
								path: "preview",
								Component: FormPreviewPage,
							},
						],
					},

					// Future dashboard routes
					// {
					//   path: "analytics",
					//   Component: Analytics,
					//   loader: async ({ request }) => {
					//     const user = await getCurrentUser();
					//     if (!user.permissions.includes('analytics.read')) {
					//       throw new Response("Unauthorized", { status: 401 });
					//     }
					//     return { analytics: await getAnalytics() };
					//   },
					// },
					{
						path: "settings",
						Component: Settings,
					},
					{
						path: "automation",
						Component: Automation,
					},
					{
						path: "automation/create",
						Component: CreateAutomationEditor,
					},
					{
						path: "automation/history/:automationId",
						Component: AutomationHistory,
					},
				],
			},
		],
	},
	{
		path: "sign-in",
		Component: SignInPage,
	},
	{
		path: "forgot-password",
		Component: ForgotPasswordPage,
	},
	{
		path: "reset-password",
		Component: ResetPasswordPage,
	},
	{
		path: "2fa",
		Component: MFAPage,
	},
	{
		path: "auth/callback",
		Component: AuthCallbackPage,
	},
]);

// export const router = createBrowserRouter([
// 	{
// 		path: "/",
// 		Component: MainLayout,
// 		children: [
// 			// Index route - renders at "/"
// 			{
// 				index: true,
// 				Component: Dashboard,
// 			},
// 			// Dashboard routes
// 			{
// 				path: "dashboard",
// 				Component: Dashboard,
// 			},
// 			{
// 				path: "dashboard/test",
// 				Component: DashboardTest,
// 			},
// 			// Future routes
// 			// {
// 			//   path: "customers",
// 			//   Component: Customers,
// 			//   loader: async ({ request }) => {
// 			//     const user = await getCurrentUser();
// 			//     if (!user.permissions.includes('customers.read')) {
// 			//       throw new Response("Unauthorized", { status: 401 });
// 			//     }
// 			//     return { customers: await getCustomers() };
// 			//   },
// 			// },
// 		],
// 	},
// ]);
