import { useUIStore } from "@/stores/uiStore";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Star, Download, Info, ChevronRight } from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { FeedbackCard } from "@/components/ui-components/FeedBackCard";
import { StarCard } from "@/components/ui-components/StarCard";
import type { MetricData } from "@/components/ui-components/FeedBackCard";
import type { StarRating } from "@/components/ui-components/StarCard";
import { InputText } from "@/components/common/InputText";
import { Checkbox } from "@/components/common/Checkbox";
import { Search, Settings2 } from "lucide-react";
import { PatientFeedbackSheet } from "@/components/dashboard/patient/PatientFeedbackSheet";
import { SurveyQuestionSheet } from "@/components/dashboard/patient/SurveyQuestionSheet";
import { ReviewsFilterSheet } from "@/components/dashboard/patient/ReviewsFilterSheet";
import { Skeleton } from "@/components/ui/skeleton";

import { LikertScaleSurveyQuestionSheet } from "@/components/dashboard/patient/LikertScaleSurveyQuestionSheet";
import { TextResponseSurveyQuestionSheet } from "@/components/dashboard/patient/TextResponseSurveyQuestionSheet";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	patientExperienceApi,
	type PatientFeedback,
	type PatientExperienceStats,
	type FeedbackSummary,
	type SummaryDetailData,
	type SummaryDetailKeyValueResponses,
	type SummaryDetailListResponse,
	type DetailedPatientFeedback,
} from "@/lib/api/patientExperienceApi";

interface Review {
	id: string;
	patientName: string;
	patientInitials: string;
	rating: number;
	comment: string;
	date: string;
	service: string;
	status: "published" | "pending" | "hidden";
	stationName: string;
	locationName: string;
	patientAvatar: string;
}

interface SummaryQuestion {
	id: string;
	question: string;
	fullQuestion?: string;
	station: string;
	location: string;
	service: string;
	responses: number;
	type: string;
}

export default function PatientReviews() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);
	const { organizationId } = useOrganizationContext();

	const [feedbacks, setFeedbacks] = useState<PatientFeedback[]>([]);
	const [isLoadingFeedbacks, setIsLoadingFeedbacks] = useState(false);
	const [feedbackError, setFeedbackError] = useState<string | null>(null);
	const [summaryQuestions, setSummaryQuestions] = useState<SummaryQuestion[]>(
		[]
	);
	const [isLoadingSummaries, setIsLoadingSummaries] = useState(false);
	const [summariesError, setSummariesError] = useState<string | null>(null);

	const getPatientInitials = (name: string): string => {
		if (!name || typeof name !== "string") {
			return "N/A";
		}
		return name
			.split(" ")
			.map((word) => word.charAt(0).toUpperCase())
			.join("")
			.slice(0, 2);
	};

	const formatDate = (dateString: string): string => {
		if (!dateString || typeof dateString !== "string") {
			return "N/A";
		}
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return dateString;
			}
			return date.toLocaleDateString("en-US", {
				day: "2-digit",
				month: "short",
				year: "numeric",
				hour: "2-digit",
				minute: "2-digit",
				hour12: true,
			});
		} catch {
			return dateString || "N/A";
		}
	};

	const transformSummaryData = (
		apiSummaries: FeedbackSummary[] | undefined
	): SummaryQuestion[] => {
		if (!apiSummaries || !Array.isArray(apiSummaries)) {
			return [];
		}
		return apiSummaries.map((summary) => ({
			id: summary?.question_id || "",
			question: summary?.question || "Unknown Question",
			fullQuestion: summary?.question || "Unknown Question",
			station: summary?.station_name || "Unknown Station",
			location: summary?.location_name || "Unknown Location",
			service: summary?.service_name || "Unknown Service",
			responses: summary?.total_response_count || 0,
			type: summary?.type_label || "Unknown Type",
		}));
	};

	const transformBarChartData = (summaryDetail: SummaryDetailData) => {
		if (!summaryDetail || Array.isArray(summaryDetail.responses)) {
			return [];
		}

		const responses =
			summaryDetail.responses as SummaryDetailKeyValueResponses;
		return Object.entries(responses).map(([label, count], index) => ({
			id: (index + 1).toString(),
			label,
			count: Number(count),
			percentage:
				summaryDetail.total_response_count > 0
					? Math.round(
							(Number(count) /
								summaryDetail.total_response_count) *
								100
						)
					: 0,
		}));
	};

	const transformPieChartData = (summaryDetail: SummaryDetailData) => {
		if (!summaryDetail || Array.isArray(summaryDetail.responses)) {
			return [];
		}

		const responses =
			summaryDetail.responses as SummaryDetailKeyValueResponses;
		const colors = [
			"bg-teal-600",
			"bg-blue-600",
			"bg-slate-700",
			"bg-orange-300",
			"bg-red-400",
		];

		return Object.entries(responses).map(([label, count], index) => ({
			id: (index + 1).toString(),
			label: label
				.replace(/_/g, " ")
				.replace(/\b\w/g, (l) => l.toUpperCase()),
			count: Number(count),
			percentage:
				summaryDetail.total_response_count > 0
					? Math.round(
							(Number(count) /
								summaryDetail.total_response_count) *
								100
						)
					: 0,
			color: colors[index % colors.length],
		}));
	};

	const transformListData = (summaryDetail: SummaryDetailData) => {
		if (!summaryDetail || !Array.isArray(summaryDetail.responses)) {
			return [];
		}

		const responses =
			summaryDetail.responses as SummaryDetailListResponse[];
		return responses.map((response, index) => ({
			id: (index + 1).toString(),
			text: response.response,
			userName: response.client_name,
			userEmail: response.client_email,
			date: new Date(response.submitted_at).toLocaleDateString("en-US", {
				day: "2-digit",
				month: "short",
				year: "numeric",
			}),
		}));
	};

	const [filter] = useState<"all" | "published" | "pending" | "hidden">(
		"all"
	);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState<"feedback" | "summary">(
		"feedback"
	);
	const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
	const [feedbackSheetOpen, setFeedbackSheetOpen] = useState(false);
	const [selectedReview, setSelectedReview] = useState<Review | null>(null);
	const [surveyQuestionSheetOpen, setSurveyQuestionSheetOpen] =
		useState(false);
	const [
		likertScaleSurveyQuestionSheetOpen,
		setLikertScaleSurveyQuestionSheetOpen,
	] = useState(false);
	const [
		textResponseSurveyQuestionSheetOpen,
		setTextResponseSurveyQuestionSheetOpen,
	] = useState(false);
	const [selectedSummaryDetail, setSelectedSummaryDetail] =
		useState<SummaryDetailData | null>(null);
	const [statsData, setStatsData] = useState<PatientExperienceStats | null>(
		null
	);
	const [isLoadingStats, setIsLoadingStats] = useState(false);
	const [detailedFeedback, setDetailedFeedback] =
		useState<DetailedPatientFeedback | null>(null);
	const [isLoadingDetailedFeedback, setIsLoadingDetailedFeedback] =
		useState(false);
	const [filterSheetOpen, setFilterSheetOpen] = useState(false);
	const [appliedFilters, setAppliedFilters] = useState({
		locations: [] as string[],
		providers: [] as string[],
		services: [] as string[],
		categories: [] as string[],
		dateRange: undefined as
			| { from: Date | undefined; to: Date | undefined }
			| undefined,
	});

	useEffect(() => {
		const fetchData = async () => {
			if (!organizationId) return;

			setIsLoadingStats(true);
			setIsLoadingFeedbacks(true);
			setIsLoadingSummaries(true);
			setFeedbackError(null);
			setSummariesError(null);

			try {
				const apiFilters = {
					organization_id: organizationId,
					per_page: 100,
					...(appliedFilters.locations.length > 0 && {
						location_ids: appliedFilters.locations.map((id) =>
							parseInt(id)
						),
					}),
					...(appliedFilters.providers.length > 0 && {
						station_ids: appliedFilters.providers.map((id) =>
							parseInt(id)
						),
					}),
					...(appliedFilters.services.length > 0 && {
						service_ids: appliedFilters.services.map((id) =>
							parseInt(id)
						),
					}),
					...(appliedFilters.categories.length > 0 && {
						category_ids: appliedFilters.categories.map((id) =>
							parseInt(id)
						),
					}),
				};

				const [statsResponse, feedbacksResponse, summariesResponse] =
					await Promise.all([
						patientExperienceApi.getPatientExperienceStats(
							organizationId
						),
						patientExperienceApi.getPatientExperience(apiFilters),
						patientExperienceApi.getFeedbackSummaries(apiFilters),
					]);

				setStatsData(statsResponse?.data || null);
				setFeedbacks(feedbacksResponse?.data || []);

				setSummaryQuestions(
					transformSummaryData(summariesResponse?.data)
				);
			} catch (error) {
				console.error(
					"Failed to fetch patient experience data:",
					error
				);
				setFeedbackError(
					"Failed to load feedback data. Please try again."
				);
				setSummariesError(
					"Failed to load summary data. Please try again."
				);
			} finally {
				setIsLoadingStats(false);
				setIsLoadingFeedbacks(false);
				setIsLoadingSummaries(false);
			}
		};

		fetchData();
	}, [organizationId, appliedFilters]);

	useEffect(() => {
		setBreadcrumbs([
			{ label: "Dashboard", href: "/" },
			{ label: "Patients", href: "/dashboard/patients" },
			{ label: "Feedback", href: "/dashboard/patients/reviews" },
		]);

		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<div>
					<h1 className="text-foreground text-2xl font-bold">
						Patient Reviews
					</h1>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline" size="sm">
						<Download className="mr-2 h-4 w-4" />
						Export
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [setPageHeaderContent]);

	const reviews = (feedbacks || []).map((feedback) => ({
		id: feedback?.id || "",
		patientName: feedback?.patient_name || "Unknown Patient",
		patientInitials: getPatientInitials(feedback?.patient_name || ""),
		rating:
			typeof feedback?.rating === "string"
				? parseFloat(feedback.rating) || 0
				: feedback?.rating || 0,
		comment: "",
		date: formatDate(feedback?.date || ""),
		service: feedback?.service_name || "Unknown Service",
		status: "published" as const,
		stationName: feedback?.station_name || "Unknown Station",
		locationName: feedback?.location_name || "Unknown Location",
		patientAvatar: feedback?.patient_avatar || "",
	}));

	const filteredReviews = reviews.filter(
		(review) => filter === "all" || review.status === filter
	);

	const searchedReviews = filteredReviews.filter(
		(review) =>
			review.patientName
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			review.service.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const searchedQuestions = (summaryQuestions || []).filter(
		(question) =>
			question.question
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			question.service.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const averageRating = statsData?.average_rating || 0;

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			setSelectedReviews(searchedReviews.map((review) => review.id));
		} else {
			setSelectedReviews([]);
		}
	};

	const handleReviewSelection = (reviewId: string, checked: boolean) => {
		if (checked) {
			setSelectedReviews((prev) => [...prev, reviewId]);
		} else {
			setSelectedReviews((prev) => prev.filter((id) => id !== reviewId));
		}
	};

	const handleSelectAllQuestions = (checked: boolean) => {
		if (checked) {
			setSelectedQuestions(
				searchedQuestions.map((question) => question.id)
			);
		} else {
			setSelectedQuestions([]);
		}
	};

	const handleQuestionSelection = (questionId: string, checked: boolean) => {
		if (checked) {
			setSelectedQuestions((prev) => [...prev, questionId]);
		} else {
			setSelectedQuestions((prev) =>
				prev.filter((id) => id !== questionId)
			);
		}
	};

	const handleViewFeedback = async (review: Review) => {
		setSelectedReview(review);
		setFeedbackSheetOpen(true);
		setDetailedFeedback(null);

		if (organizationId && review.id) {
			try {
				setIsLoadingDetailedFeedback(true);
				const detailedFeedbackResponse =
					await patientExperienceApi.getPatientFeedbackDetail(
						review.id,
						organizationId
					);

				if (
					detailedFeedbackResponse?.success &&
					detailedFeedbackResponse?.data
				) {
					setDetailedFeedback(detailedFeedbackResponse.data);
				}
			} catch (error: any) {
				console.error(
					"Error fetching detailed feedback:",
					error?.response?.data
				);
			} finally {
				setIsLoadingDetailedFeedback(false);
			}
		}
	};

	const handleViewSurveyQuestion = async (question: SummaryQuestion) => {
		if (organizationId && question.id) {
			try {
				const summaryDetail =
					await patientExperienceApi.getSummaryDetail(
						question.id,
						organizationId
					);

				setSelectedSummaryDetail(summaryDetail?.data || null);
				const uiView = summaryDetail?.data?.ui_view;

				if (uiView === "bar") {
					setSurveyQuestionSheetOpen(true);
				} else if (uiView === "pie") {
					setLikertScaleSurveyQuestionSheetOpen(true);
				} else if (uiView === "list") {
					setTextResponseSurveyQuestionSheetOpen(true);
				} else {
					setSurveyQuestionSheetOpen(true);
				}
			} catch (error: any) {
				setSurveyQuestionSheetOpen(true);
			}
		} else {
			setSurveyQuestionSheetOpen(true);
		}
	};

	const renderStars = (rating: number) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				className={`h-3.5 w-3.5 ${
					index < rating
						? "fill-amber-500 text-amber-500"
						: "fill-gray-300 text-gray-300"
				}`}
			/>
		));
	};

	if (!isLoadingFeedbacks || !isLoadingSummaries) {
		return (
			<div className="flex h-full flex-col space-y-6 pt-2">
				{/* Statistics Cards Skeleton */}
				<div className="inline-flex items-start justify-start gap-3 self-stretch">
					<div className="flex h-32 w-80 flex-shrink-0 rounded-lg border border-gray-200 p-4">
						<div className="flex w-full flex-col gap-3">
							<div className="flex items-center gap-2">
								<Skeleton className="h-8 w-8 rounded-full" />
								<Skeleton className="h-6 w-16" />
							</div>
							<Skeleton className="h-4 w-24" />
							<div className="flex gap-2">
								<Skeleton className="h-16 w-16" />
								<Skeleton className="h-16 w-16" />
							</div>
						</div>
					</div>
					<div className="flex h-32 flex-1 rounded-lg border border-gray-200 p-4">
						<div className="flex w-full flex-col gap-2">
							<Skeleton className="h-6 w-32" />
							<div className="space-y-1">
								{Array.from({ length: 5 }).map((_, index) => (
									<div
										key={index}
										className="flex items-center gap-2"
									>
										<Skeleton className="h-3 w-12" />
										<Skeleton className="h-2 w-20" />
										<Skeleton className="h-3 w-8" />
									</div>
								))}
							</div>
						</div>
					</div>
				</div>

				{/* Tab System and Search Skeleton */}
				<div className="inline-flex flex-col items-start justify-start gap-1 self-stretch rounded-2xl">
					<div className="inline-flex items-center justify-between self-stretch">
						<div className="flex h-10 items-center justify-start rounded-lg bg-[#F4F4F5] p-1">
							<Skeleton className="h-8 w-16 rounded-md" />
							<Skeleton className="h-8 w-16 rounded-md" />
						</div>
						<div className="flex w-[636px] items-center justify-end gap-2.5">
							<Skeleton className="h-9 w-9" />
							<Skeleton className="h-9 w-56" />
						</div>
					</div>

					{/* Table Skeleton */}
					<div className="flex flex-col items-start justify-start gap-1 self-stretch overflow-hidden rounded-xl border border-[#E4E4E7]">
						{/* Table Header */}
						<div className="inline-flex items-center justify-start self-stretch">
							<div className="flex h-12 items-center justify-start gap-2.5 px-4">
								<Skeleton className="h-3 w-3" />
							</div>
							<div className="flex h-12 min-w-20 flex-1 items-center justify-start gap-2.5 px-3">
								<Skeleton className="h-4 w-20" />
							</div>
							<div className="flex h-12 w-40 items-center justify-start gap-2.5 px-3">
								<Skeleton className="h-4 w-12" />
							</div>
							<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
								<Skeleton className="h-4 w-16" />
							</div>
							<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
								<Skeleton className="h-4 w-12" />
							</div>
							<div className="flex h-12 w-36 min-w-20 items-center justify-start gap-2.5 px-3">
								<Skeleton className="h-4 w-12" />
							</div>
							<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
								<Skeleton className="h-4 w-20" />
							</div>
							<div className="flex h-12 w-16 min-w-16 items-center justify-end gap-2.5 px-3" />
						</div>

						{/* Table Rows */}
						{Array.from({ length: 6 }).map((_, index) => (
							<div
								key={index}
								className="inline-flex h-16 items-center justify-start self-stretch border-t border-[#E4E4E7] bg-white"
							>
								<div className="flex items-center justify-start gap-2.5 self-stretch px-4">
									<Skeleton className="h-3 w-3" />
								</div>
								<div className="flex min-w-20 flex-1 items-center justify-start gap-2 self-stretch px-3">
									<Skeleton className="h-9 w-9 rounded-full" />
									<Skeleton className="h-4 w-32" />
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<Skeleton className="h-4 w-24" />
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<Skeleton className="h-4 w-28" />
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<Skeleton className="h-4 w-20" />
								</div>
								<div className="flex w-36 min-w-20 items-center justify-start gap-2 self-stretch px-3">
									<div className="flex items-center justify-start gap-1.5">
										{Array.from({ length: 5 }).map(
											(_, starIndex) => (
												<Skeleton
													key={starIndex}
													className="h-3.5 w-3.5"
												/>
											)
										)}
									</div>
									<Skeleton className="h-4 w-6" />
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<Skeleton className="h-4 w-24" />
								</div>
								<div className="flex min-w-16 items-center justify-end gap-1.5 self-stretch px-3">
									<Skeleton className="h-6 w-6" />
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		);
	}

	if (feedbackError || summariesError) {
		return (
			<div className="flex h-full flex-col items-center justify-center">
				<div className="mb-4 text-red-500">
					{feedbackError || summariesError}
				</div>
				<Button
					onClick={() => window.location.reload()}
					variant="outline"
				>
					Retry
				</Button>
			</div>
		);
	}

	const submissionMetrics: MetricData[] = [
		{
			value: statsData?.total_submission_count?.toString() || "0",
			label: "Submission",
			percentage: statsData?.completion_rate || 0,
			primaryColor: "#4F46E5",
			secondaryColor: "#CBD5E1",
			primaryLabel: "Completed",
			secondaryLabel: "Incomplete",
		},
		{
			value: `${statsData?.completion_rate || 0}%`,
			label: "Completion Rate",
			percentage: statsData?.completion_rate || 0,
			primaryColor: "#EF4444",
			secondaryColor: "#FCA5A5",
			primaryLabel: "Submitted",
			secondaryLabel: "Drop-Off",
		},
	];

	const totalRatings = statsData
		? Object.values(statsData.distribution).reduce(
				(sum: number, count: number) => sum + count,
				0
			)
		: 0;

	const starRatings: StarRating[] = [
		{
			stars: 1,
			count: statsData?.distribution["1"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.distribution["1"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 2,
			count: statsData?.distribution["2"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.distribution["2"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 3,
			count: statsData?.distribution["3"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.distribution["3"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 4,
			count: statsData?.distribution["4"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.distribution["4"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
		{
			stars: 5,
			count: statsData?.distribution["5"] || 0,
			percentage:
				totalRatings > 0
					? Math.round(
							((statsData?.distribution["5"] || 0) /
								totalRatings) *
								100
						)
					: 0,
		},
	];

	return (
		<div className="flex h-full flex-col space-y-6 pt-2">
			<div className="inline-flex items-start justify-start gap-3 self-stretch">
				{!isLoadingStats ? (
					<>
						<div className="flex h-32 w-80 flex-shrink-0 rounded-lg border border-gray-200 p-4">
							<div className="flex w-full flex-col gap-3">
								<div className="flex items-center gap-2">
									<Skeleton className="h-8 w-8 rounded-full" />
									<Skeleton className="h-6 w-16" />
								</div>
								<Skeleton className="h-4 w-24" />
								<div className="flex gap-2">
									<Skeleton className="h-16 w-16" />
									<Skeleton className="h-16 w-16" />
								</div>
							</div>
						</div>
						<div className="flex h-32 flex-1 rounded-lg border border-gray-200 p-4">
							<div className="flex w-full flex-col gap-2">
								<Skeleton className="h-6 w-32" />
								<div className="space-y-1">
									{Array.from({ length: 5 }).map(
										(_, index) => (
											<div
												key={index}
												className="flex items-center gap-2"
											>
												<Skeleton className="h-3 w-12" />
												<Skeleton className="h-2 w-20" />
												<Skeleton className="h-3 w-8" />
											</div>
										)
									)}
								</div>
							</div>
						</div>
					</>
				) : (
					<>
						<FeedbackCard
							rating={averageRating}
							reviewCount={statsData?.total_reviews || 0}
							metrics={submissionMetrics}
							className="flex-shrink-0"
						/>
						<StarCard
							ratings={starRatings}
							barColor="bg-amber-500"
							className=""
						/>
					</>
				)}
			</div>

			<div className="inline-flex flex-col items-start justify-start gap-1 self-stretch rounded-2xl">
				<div className="inline-flex items-center justify-between self-stretch">
					<div className="flex h-10 items-center justify-start rounded-lg bg-[#F4F4F5] p-1">
						<div
							className={`flex h-8 cursor-pointer items-center justify-center gap-2 rounded-md px-3 py-1 ${
								activeTab === "feedback"
									? "bg-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]"
									: ""
							}`}
							onClick={() => setActiveTab("feedback")}
						>
							<div
								className={`justify-start text-center text-xs leading-none ${
									activeTab === "feedback"
										? "font-semibold text-gray-900"
										: "font-medium text-gray-500"
								}`}
							>
								Feedback
							</div>
						</div>
						<div
							className={`flex h-8 cursor-pointer items-center justify-center gap-2 rounded-md px-3 py-1 ${
								activeTab === "summary"
									? "bg-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]"
									: ""
							}`}
							onClick={() => setActiveTab("summary")}
						>
							<div
								className={`justify-start text-center text-xs leading-none ${
									activeTab === "summary"
										? "font-semibold text-gray-900"
										: "font-medium text-gray-500"
								}`}
							>
								Summary
							</div>
						</div>
					</div>
					<div className="flex w-[636px] items-center justify-end gap-2.5">
						<Button
							variant="outline"
							size="icon"
							className="h-9 w-9"
							onClick={() => setFilterSheetOpen(true)}
						>
							<Settings2 className="h-4 w-4" />
						</Button>
						<InputText
							variant="with-icon"
							icon={<Search className="h-3 w-3" />}
							iconPosition="left"
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="h-9 w-56"
						/>
					</div>
				</div>

				<div className="flex flex-col items-start justify-start gap-1 self-stretch overflow-hidden rounded-xl border border-[#E4E4E7]">
					{activeTab === "feedback" ? (
						<>
							{searchedReviews.length > 0 ? (
								<div className="inline-flex items-center justify-start self-stretch">
									<div className="flex h-12 items-center justify-start gap-2.5 px-4">
										<Checkbox
											checked={
												searchedReviews.length > 0 &&
												searchedReviews.every(
													(review) =>
														selectedReviews.includes(
															review.id
														)
												)
											}
											onCheckedChange={handleSelectAll}
											className="h-3 w-3 border-[#E4E4E7] [&_svg]:h-2 [&_svg]:w-2 [&>*]:flex [&>*]:items-center [&>*]:justify-center"
										/>
									</div>
									<div className="flex h-12 min-w-20 flex-1 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Client Name
										</div>
									</div>
									<div className="flex h-12 w-40 items-center justify-start gap-2.5 px-3">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											Station
										</div>
									</div>
									<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Location
										</div>
									</div>
									<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Service
										</div>
									</div>
									<div className="flex h-12 w-36 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Rating
										</div>
									</div>
									<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Date Added
										</div>
									</div>
									<div className="flex h-12 w-16 min-w-16 items-center justify-end gap-2.5 px-3" />
								</div>
							) : (
								<div className="flex min-h-[500px] w-full items-center justify-center p-8">
									<EmptyContent
										title="No Feedback Found"
										description="Patient feedback will appear here once they start providing responses."
										// actions={[
										// 	{
										// 		label: "View Settings",
										// 		onClick: () =>
										// 			console.log(
										// 				"View settings"
										// 			),
										// 		variant: "primary",
										// 	},
										// ]}
										variant="compact"
										width="w-auto"
									/>
								</div>
							)}
						</>
					) : (
						<>
							{searchedQuestions.length > 0 ? (
								<div className="inline-flex items-center justify-start self-stretch">
									<div className="flex h-12 items-center justify-start gap-2.5 px-4">
										<Checkbox
											checked={
												searchedQuestions.length > 0 &&
												searchedQuestions.every(
													(question) =>
														selectedQuestions.includes(
															question.id
														)
												)
											}
											onCheckedChange={
												handleSelectAllQuestions
											}
											className="h-3 w-3 border-[#E4E4E7] [&_svg]:h-2 [&_svg]:w-2 [&>*]:flex [&>*]:items-center [&>*]:justify-center"
										/>
									</div>
									<div className="flex h-12 min-w-20 flex-1 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Question
										</div>
									</div>
									<div className="flex h-12 w-40 items-center justify-start gap-2.5 px-3">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											Station
										</div>
									</div>
									<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Location
										</div>
									</div>
									<div className="flex h-12 w-40 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Service
										</div>
									</div>
									<div className="flex h-12 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											No.of Reponses
										</div>
									</div>
									<div className="flex h-12 w-36 min-w-20 items-center justify-start gap-2.5 px-3">
										<div className="flex-1 justify-center text-xs leading-none font-normal text-gray-500">
											Type
										</div>
									</div>
									<div className="flex h-12 w-16 min-w-16 items-center justify-end gap-2.5 px-3" />
								</div>
							) : (
								<div className="flex min-h-[500px] w-full items-center justify-center p-8">
									<EmptyContent
										title="No Survey Data Found"
										description="Survey question data will appear here once they start providing responses."
										// actions={[
										// 	{
										// 		label: "View Settings",
										// 		onClick: () =>
										// 			console.log(
										// 				"View settings"
										// 			),
										// 		variant: "primary",
										// 	},
										// ]}
										variant="compact"
										width="w-auto"
									/>
								</div>
							)}
						</>
					)}

					{/* Table Rows */}
					{activeTab === "feedback" &&
						searchedReviews.length > 0 &&
						searchedReviews.map((review) => (
							<div
								key={review.id}
								className="inline-flex h-16 cursor-pointer items-center justify-start self-stretch border-t border-[#E4E4E7] bg-white hover:bg-gray-50"
								onClick={() => handleViewFeedback(review)}
							>
								<div
									className="flex items-center justify-start gap-2.5 self-stretch px-4"
									onClick={(e) => e.stopPropagation()}
								>
									<Checkbox
										checked={selectedReviews.includes(
											review.id
										)}
										onCheckedChange={(checked) =>
											handleReviewSelection(
												review.id,
												checked
											)
										}
										className="h-3 w-3 border-[#E4E4E7] [&_svg]:h-2 [&_svg]:w-2 [&>*]:flex [&>*]:items-center [&>*]:justify-center"
									/>
								</div>
								<div className="flex min-w-20 flex-1 items-center justify-start gap-2 self-stretch px-3">
									<Avatar className="h-9 w-9">
										{review.patientAvatar ? (
											<img
												src={review.patientAvatar}
												alt={review.patientName}
												className="h-full w-full object-cover"
											/>
										) : (
											<AvatarFallback className="bg-gray-100">
												{review.patientInitials}
											</AvatarFallback>
										)}
									</Avatar>
									<div className="justify-center text-sm leading-tight font-medium text-gray-900">
										{review.patientName}
									</div>
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{review.stationName}
										</div>
									</div>
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{review.locationName}
										</div>
									</div>
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{review.service}
										</div>
									</div>
								</div>
								<div className="flex w-36 min-w-20 items-center justify-start gap-2 self-stretch px-3">
									<div className="flex items-center justify-start gap-1.5">
										{renderStars(review.rating)}
									</div>
									<div className="justify-center text-xs leading-none font-normal text-gray-500">
										{review.rating}
									</div>
								</div>
								<div className="flex w-40 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{review.date}
										</div>
									</div>
								</div>
								<div
									className="flex min-w-16 items-center justify-end gap-1.5 self-stretch px-3"
									onClick={(e) => e.stopPropagation()}
								>
									<Button
										variant="outline"
										size="icon"
										className="h-6 w-6 p-2"
										onClick={() =>
											handleViewFeedback(review)
										}
									>
										<Info className="h-3 w-3" />
									</Button>
								</div>
							</div>
						))}

					{activeTab === "summary" &&
						searchedQuestions.length > 0 &&
						searchedQuestions.map((question) => (
							<div
								key={question.id}
								className="inline-flex h-16 cursor-pointer items-center justify-start self-stretch border-t border-[#E4E4E7] bg-white hover:bg-gray-50"
								onClick={() =>
									handleViewSurveyQuestion(question)
								}
							>
								<div
									className="flex items-center justify-start gap-2.5 self-stretch px-4"
									onClick={(e) => e.stopPropagation()}
								>
									<Checkbox
										checked={selectedQuestions.includes(
											question.id
										)}
										onCheckedChange={(checked) =>
											handleQuestionSelection(
												question.id,
												checked
											)
										}
										className="h-3 w-3 border-[#E4E4E7] [&_svg]:h-2 [&_svg]:w-2 [&>*]:flex [&>*]:items-center [&>*]:justify-center"
									/>
								</div>
								<div className="flex min-w-20 flex-1 items-center justify-start gap-3 self-stretch px-3">
									<div className="justify-center text-sm leading-tight font-medium text-gray-900">
										{question.question}
									</div>
								</div>
								<div className="flex h-16 w-40 min-w-20 items-center justify-start gap-3 px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{question.station}
										</div>
									</div>
								</div>
								<div className="flex h-16 w-40 min-w-20 items-center justify-start gap-3 px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{question.location}
										</div>
									</div>
								</div>
								<div className="flex h-16 w-40 min-w-20 items-center justify-start gap-3 px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{question.service}
										</div>
									</div>
								</div>
								<div className="flex w-28 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<div className="flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
										<div className="justify-start text-[10px] leading-3 font-medium text-gray-900">
											{question.responses}
										</div>
									</div>
								</div>
								<div className="flex w-36 min-w-20 items-center justify-start gap-3 self-stretch px-3">
									<div className="inline-flex flex-col items-start justify-start gap-0.5">
										<div className="justify-center text-xs leading-none font-normal text-gray-500">
											{question.type}
										</div>
									</div>
								</div>
								<div
									className="flex min-w-16 items-center justify-end gap-1.5 self-stretch px-3"
									onClick={(e) => e.stopPropagation()}
								>
									<Button
										variant="outline"
										size="icon"
										className="h-6 w-6 p-2"
										onClick={() =>
											handleViewSurveyQuestion(question)
										}
									>
										<ChevronRight className="h-3 w-3" />
									</Button>
								</div>
							</div>
						))}
				</div>
			</div>

			<PatientFeedbackSheet
				open={feedbackSheetOpen}
				onOpenChange={setFeedbackSheetOpen}
				feedbackData={
					selectedReview
						? {
								patientName: selectedReview.patientName,
								patientInitials: selectedReview.patientInitials,
								date: selectedReview.date,
								rating: selectedReview.rating,
								service: selectedReview.service,
								doctor: "Dr. Steven Brown",
								location: selectedReview.locationName,
								questions: [
									{
										id: "1",
										question:
											"I found booking my appointment online easy.",
										answer: "Strongly Agree",
									},
									{
										id: "2",
										question:
											"I was able to select an appointment time that was convenient for me.",
										answer: "Agree",
									},
								],
								feedback: selectedReview.comment,
							}
						: undefined
				}
				detailedFeedback={detailedFeedback}
				isLoading={isLoadingDetailedFeedback}
			/>

			{/* for ui_view: "bar" */}
			<SurveyQuestionSheet
				open={surveyQuestionSheetOpen}
				onOpenChange={setSurveyQuestionSheetOpen}
				questionData={
					selectedSummaryDetail &&
					selectedSummaryDetail.ui_view === "bar"
						? {
								question: selectedSummaryDetail.question,
								type: selectedSummaryDetail.type_label,
								totalResponses:
									selectedSummaryDetail.total_response_count,
								responses: transformBarChartData(
									selectedSummaryDetail
								),
							}
						: undefined
				}
			/>

			{/*  for ui_view: "pie" */}
			<LikertScaleSurveyQuestionSheet
				open={likertScaleSurveyQuestionSheetOpen}
				onOpenChange={setLikertScaleSurveyQuestionSheetOpen}
				questionData={
					selectedSummaryDetail &&
					selectedSummaryDetail.ui_view === "pie"
						? {
								question: selectedSummaryDetail.question,
								type: selectedSummaryDetail.type_label,
								totalResponses:
									selectedSummaryDetail.total_response_count,
								responses: transformPieChartData(
									selectedSummaryDetail
								) as any,
							}
						: undefined
				}
			/>

			{/*  for ui_view: "list" */}
			<TextResponseSurveyQuestionSheet
				open={textResponseSurveyQuestionSheetOpen}
				onOpenChange={setTextResponseSurveyQuestionSheetOpen}
				questionData={
					selectedSummaryDetail &&
					selectedSummaryDetail.ui_view === "list"
						? {
								question: selectedSummaryDetail.question,
								type: selectedSummaryDetail.type_label,
								totalResponses:
									selectedSummaryDetail.total_response_count,
								responses: transformListData(
									selectedSummaryDetail
								),
							}
						: undefined
				}
			/>

			<ReviewsFilterSheet
				open={filterSheetOpen}
				onOpenChange={setFilterSheetOpen}
				onApplyFilters={setAppliedFilters}
				onResetFilters={() =>
					setAppliedFilters({
						locations: [],
						providers: [],
						services: [],
						categories: [],
						dateRange: undefined,
					})
				}
			/>
		</div>
	);
}
