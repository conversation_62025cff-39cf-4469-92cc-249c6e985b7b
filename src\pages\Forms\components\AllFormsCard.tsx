import { type FC } from "react";
import { Trash2, Pencil, Send } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import * as Types from "../types";
import { Badge } from "@/components/ui/badge";
import { getVariantColor } from "../utils/formHelpers";
import { format } from "date-fns";

export interface AllFormsCardProps {
	form: Types.FormTypes.FormTypes;
	onEdit?: (form: Types.FormTypes.FormTypes) => void;
	onView?: (form: Types.FormTypes.FormTypes) => void;
	onDelete?: (formId: string) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
}

export const AllFormsCard: FC<AllFormsCardProps> = ({
	form,
	onEdit,
	onView,
	onDelete,
	isSelected = false,
	onSelectionChange,
}) => {
	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(form!)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Form Name Section */}
			<div className="flex flex-2 items-center px-3">
				<h2 className="text-base leading-5 font-medium text-wrap">
					{form?.name}
				</h2>
			</div>

			{/* Type Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className={getVariantColor(
						form?.type,
						"border-none text-xs font-medium"
					)}
				>
					{form?.type}
				</Badge>
			</div>

			{/* Service Section */}
			<div className="flex flex-1 items-center gap-1.5 px-3">
				<h2 className="text-muted text-sm leading-5 font-normal text-wrap">
					{form?.services
						?.map((service) => service.name)
						.join(", ") || "-"}
				</h2>
			</div>

			{/* Providers Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className="tsxt-sm bg-foreground-muted border-transparent px-2 py-1 font-medium text-[#0a2914]"
				>
					{form?.providers?.length > 0
						? form?.providers?.length
						: "All"}
				</Badge>
			</div>

			{/* Status Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className={getVariantColor(
						form?.status,
						"border-transparent text-xs"
					)}
				>
					{form?.status}
				</Badge>
			</div>

			{/* Created At Section */}
			<div className="flex flex-1 items-center px-3">
				<h3 className="text-muted text-sm leading-5 font-normal text-wrap">
					{format(new Date(form?.created_at), "dd MMM yyyy")}
				</h3>
			</div>

			{/* Actions Section */}
			<div className="flex flex-1 items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(form!);
						}}
					>
						<Pencil className="text-muted size-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(form!);
						}}
					>
						<Send className="text-muted size-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onDelete?.(form?.id);
						}}
					>
						<Trash2 className="text-muted size-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
