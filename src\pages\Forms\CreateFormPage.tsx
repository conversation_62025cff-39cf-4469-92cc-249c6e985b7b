import { useEffect, useState, type FC } from "react";
import { useUIStore } from "@/stores/uiStore";
import { <PERSON>an<PERSON>ye, Upload, Trash2, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { createField, generateUUID } from "./utils/formHelpers";
import { formBuilderSchema } from "./schema/form";
import type { FormTypes } from "./types";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { LuMinus } from "react-icons/lu";
import { FieldControl, FormFlowSelect, DraggableFormField } from "./components";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	DndContext,
	closestCenter,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useFieldArray } from "react-hook-form";
import { Uploader } from "@/components/common/Uploader";
import { MultiSelectDropdown } from "@/components/common/MultiSelectDropdown";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";
import { useServices } from "@/features/locations/hooks/useServices";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useCreateForm } from "./store/slices/formSlice";
import { useFormWithApi } from "@/hooks/useFormWithApi";
import { useNavigate, useLocation } from "react-router";
import { useFormDraftStore } from "./store/slices/formDraftSlice";
import { useAutoSave } from "./hooks/useAutoSave";

const formTypes = {
	data: [
		{ id: 1, name: "Service", value: "service" },
		{ id: 2, name: "General Inquiry", value: "enquiry" },
		{ id: 3, name: "Intake", value: "intake" },
		{ id: 4, name: "Feedback", value: "feedback" },
	],
};

export const CreateFormPage: FC = () => {
	const navigate = useNavigate();
	const location = useLocation();
	const [activeSection, setActiveSection] = useState<string | null>(null);
	const [activeDragId, setActiveDragId] = useState<string | null>(null);
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const setShowHeaderDate = useUIStore((state) => state.setShowHeaderDate);
	const { organizationId } = useOrganizationContext();

	const sensors = useSensors(
		useSensor(PointerSensor),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	const { mutate: createForm, isPending: isCreatingForm } = useCreateForm();

	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);
	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);
	const { data: servicesData, isLoading: isLoadingServices } = useServices({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
	});
	const [showSendFormLinkSheet, setShowSendFormLinkSheet] = useState(false);

	// Draft management
	const {
		currentDraft,
		updateCurrentDraft,
		saveDraft,
		isDirty,
		setCurrentDraft,
		createDraft,
	} = useFormDraftStore();

	const form = useFormWithApi<FormTypes.FormDataType>({
		schema: formBuilderSchema,
		defaultValues: currentDraft || {
			name: "",
			type: "service",
			logo_url: null,
			banner_url: null,
			success_message: "We have received your form.",
			block_message: "Access to this form has been restricted.",
			submit_button_title: "Submit",
			service_ids: [],
			location_ids: [],
			station_ids: [],
			apply_to: [],
			status: "draft",
			sections: [
				{
					id: crypto.randomUUID(),
					title: "",
					description: null,
					order: 1,
					flow_action: "submit",
					flow_target_section_id: undefined,
					fields: [
						{
							id: crypto.randomUUID(),
							title: "",
							type: "text",
							order: 1,
							required: false,
							options: [],
							description: null,
							image: null,
							info_text_value: null,
							approved_formats: [],
						},
					],
					flow: {
						action: "submit",
						targetSection: undefined as string | undefined,
					},
				},
			],
			collect_rating: false,
			collect_general_feedback: false,
			general_feedback_title: "General Feedback",
			is_auto_approve: false,
		},
		mode: "onChange",
	});

	// Initialize draft on component mount
	useEffect(() => {
		// Check if coming back from preview with existing data
		if (location.state?.formData && location.state?.fromPreview) {
			const formData = location.state.formData;
			form.reset(formData);
			updateCurrentDraft(formData);
		} else if (!currentDraft) {
			// Create initial draft if none exists
			const initialData = form.getValues();
			const draftId = createDraft(initialData, "New Form");
			setCurrentDraft(draftId);
		} else {
			// Load existing draft
			form.reset(currentDraft);
		}
	}, []);

	// Auto-save functionality
	const { saveNow } = useAutoSave(form.watch());

	// Watch for form changes and update draft store
	useEffect(() => {
		const subscription = form.watch((data) => {
			if (data && Object.keys(data).length > 0) {
				updateCurrentDraft(data as FormTypes.FormDataType);
			}
		});
		return () => subscription.unsubscribe();
	}, [form, updateCurrentDraft]);

	// Handle preview navigation
	const handlePreview = () => {
		const currentFormData = form.getValues();

		// Save current state before navigating
		const draftId = saveDraft(
			currentFormData,
			currentFormData.name || "Untitled Form"
		);

		// Navigate to preview with state
		navigate("/dashboard/forms/preview", {
			state: {
				formData: currentFormData,
				draftId,
				returnTo: "/dashboard/forms/create",
			},
		});
	};

	// Before unload warning for unsaved changes
	useEffect(() => {
		const handleBeforeUnload = (e: BeforeUnloadEvent) => {
			if (isDirty) {
				e.preventDefault();
				e.returnValue =
					"You have unsaved changes. Are you sure you want to leave?";
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		return () =>
			window.removeEventListener("beforeunload", handleBeforeUnload);
	}, [isDirty]);

	const {
		fields: sections,
		append: appendSection,
		remove: removeSection,
	} = useFieldArray({
		control: form.control,
		name: "sections",
	});

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Dashboard",
				href: "/",
			},
			{
				label: "Forms",
				href: "/dashboard/forms",
			},
			{
				label: "Form Manager",
				href: "/dashboard/forms",
			},
			{
				label: "Create a Form",
				href: "/dashboard/forms/create",
			},
		]);

		setCurrentPageTitle("Form Manager");
		setShowHeaderDate(false);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, setCurrentPageTitle, setShowHeaderDate]);

	const addField = (sectionIndex: number, type: FormTypes.FieldType) => {
		const currentFields = form.getValues(`sections.${sectionIndex}.fields`);
		const newField = createField(type, currentFields.length + 1);

		form.setValue(
			`sections.${sectionIndex}.fields`,
			[...currentFields, newField] as any,
			{
				shouldValidate: false,
				shouldDirty: true,
				shouldTouch: false,
			}
		);
	};

	const addSection = (sectionIndex: number) => {
		const newSectionId = generateUUID();

		const newSection: FormTypes.FormSection = {
			id: newSectionId,
			title: "",
			description: null,
			order: sectionIndex + 1,
			flow_action: "submit",
			flow_target_section_id: undefined,
			fields: [
				{
					id: generateUUID(),
					title: "",
					type: "text" as FormTypes.FieldType,
					order: 1,
					required: false,
					options: [],
					description: null,
					image: null,
					info_text_value: null,
					approved_formats: [],
				},
			],
			flow: {
				action: "submit",
				targetSection: undefined,
			},
		};

		const currentSections = form.getValues("sections");
		if (currentSections.length > 0) {
			form.setValue(`sections.${currentSections.length - 1}.flow`, {
				action: "continue",
				targetSection: newSectionId,
			});
		}

		appendSection(newSection);
	};

	const addIntakeFirstSection = () => {
		const newSectionId = generateUUID();

		const newSection: FormTypes.FormSection = {
			id: newSectionId,
			title: "",
			description: "",
			order: 1,
			flow_action: "submit",
			flow_target_section_id: undefined,
			fields: [
				{
					id: generateUUID(),
					title: "Full Name",
					type: "text" as FormTypes.FieldType,
					order: 1,
					required: true,
					options: [],
					description: "",
					image: null,
					info_text_value: null,
					approved_formats: [],
				},
				{
					id: generateUUID(),
					title: "Email",
					type: "text" as FormTypes.FieldType,
					order: 2,
					required: true,
					options: [],
					description: "",
					image: null,
					info_text_value: null,
					approved_formats: [],
				},
				{
					id: generateUUID(),
					title: "Validator Field 1",
					type: "text" as FormTypes.FieldType,
					order: 3,
					required: true,
					options: [],
					description: "",
					image: null,
					info_text_value: null,
					approved_formats: [],
				},
				{
					id: generateUUID(),
					title: "Validator Field 2",
					type: "text" as FormTypes.FieldType,
					order: 4,
					required: true,
					options: [],
					description: "",
					image: null,
					info_text_value: null,
					approved_formats: [],
				},
			],
			flow: {
				action: "submit",
				targetSection: undefined,
			},
		};

		// Clear existing sections and add the new intake section at index 0
		form.setValue("sections", [newSection]);
	};

	const moveField = (
		fromSectionIndex: number,
		fromFieldIndex: number,
		toSectionIndex: number,
		toFieldIndex: number
	) => {
		const sections = form.getValues("sections");
		const fromSection = sections[fromSectionIndex];
		const [movedField] = fromSection.fields.splice(fromFieldIndex, 1);
		const toSection = sections[toSectionIndex];
		toSection.fields.splice(toFieldIndex, 0, movedField);

		form.setValue("sections", sections, {
			shouldValidate: true,
			shouldDirty: true,
			shouldTouch: true,
		});
	};

	const handleDeleteSection = (sectionId: string) => {
		const sectionIndex = sections.findIndex(
			(section) => section.id === sectionId
		);

		if (sectionIndex !== -1) {
			const formSections = form.getValues("sections");

			formSections.forEach((section, sIdx) => {
				section.fields.forEach((field: any, fIdx) => {
					if (field.options?.length) {
						const needsUpdate = field.options.some(
							(opt: any) =>
								opt.conditions?.destination === sectionId
						);

						if (needsUpdate) {
							field.options.forEach(
								(option: any, optIdx: number) => {
									if (
										option.conditions?.destination ===
										sectionId
									) {
										form.setValue(
											`sections.${sIdx}.fields.${fIdx}.options.${optIdx}.conditions` as any,
											{
												type: "continue",
												destination: "next",
												logic: option.conditions.logic,
												selected:
													option.conditions.selected,
												conditional_block_message:
													option.conditions
														.conditional_block_message,
											}
										);
									}
								}
							);
						}
					}
				});
			});

			removeSection(sectionIndex);
		}
	};

	const handleDragStart = (event: any) => {
		setActiveDragId(event.active.id);
	};

	const handleDragEnd = (event: any) => {
		const { active, over } = event;
		setActiveDragId(null);

		if (!over) return;

		if (active.id !== over.id) {
			const activeData = active.data.current;
			const overData = over.data.current;

			moveField(
				activeData.sectionIndex,
				activeData.fieldIndex,
				overData.sectionIndex,
				overData.fieldIndex
			);
		}
	};

	const onSubmit = (data: FormTypes.FormDataType) => {
		createForm(
			{ data, organizationId: organizationId! },
			{
				onError: (error) => {
					// This handles server validation errors automatically
					form.handleApiError(error);
				},
				onSuccess: () => {
					form.handleApiSuccess("Success!");
					form.reset();
					navigate("/dashboard/forms");
				},
			}
		);
	};

	const [descriptionVisibility, setDescriptionVisibility] = useState<{
		[key: string]: boolean;
	}>(
		sections.reduce(
			(acc, section) => {
				acc[section.id] = section.description !== null;
				return acc;
			},
			{} as { [key: string]: boolean }
		)
	);

	const handleToggleDescription = (sectionId: string) => {
		setDescriptionVisibility((prev) => ({
			...prev,
			[sectionId]: !prev[sectionId],
		}));
	};

	const formType = form.watch("type");

	// // Call addIntakeFirstSection when form type changes to "intake"
	// useEffect(() => {
	// 	if (formType === "intake") {
	// 		addIntakeFirstSection();
	// 	}
	// }, [formType, addIntakeFirstSection]);

	return (
		<DndContext
			sensors={sensors}
			collisionDetection={closestCenter}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
		>
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-6"
				>
					<div className="flex h-full max-h-[calc(100vh-180px)] flex-col gap-4">
						{/* Header */}

						{/* Form Builder */}
						<div className="grid w-full max-w-screen grid-cols-[400px_1fr] gap-x-7 divide-x overflow-y-hidden">
							<div className="flex w-full flex-col space-y-6 pr-7.5 pl-4">
								<div className="flex items-center justify-start pt-6">
									<h1 className="text-2xl font-bold">
										Create a Form
									</h1>
								</div>
								<FormField
									control={form.control}
									name="type"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Form Type{" "}
											</FormLabel>
											<Select
												onValueChange={(value) => {
													field.onChange(value);
													form.setValue(
														"apply_to",
														[]
													);
													form.setValue(
														"service_ids",
														[]
													);
													form.setValue(
														"location_ids",
														[]
													);
													form.setValue(
														"station_ids",
														[]
													);
													if (value === "intake") {
														addIntakeFirstSection();
													}
												}}
												value={field.value || ""}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select" />
												</SelectTrigger>
												<SelectContent>
													{/* <SelectItem value="no_service">
														No service
													</SelectItem> */}
													{formTypes?.data.map(
														(type: any) => (
															<SelectItem
																key={type.id}
																value={
																	type.value
																}
															>
																{type.name}
															</SelectItem>
														)
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{(formType === "service" ||
									formType === "feedback") && (
									<FormField
										control={form.control}
										name="service_ids"
										render={({
											field: { value, onChange },
										}) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Services{" "}
												</FormLabel>
												<MultiSelectDropdown
													value={value || []}
													onValueChange={(value) =>
														onChange(value)
													}
													placeholder="Select Services"
													label="Select Services"
													options={
														servicesData?.data?.map(
															(service) => ({
																value: service.id.toString(),
																label: service.name,
															})
														) || []
													}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								{(formType === "service" ||
									formType === "feedback" ||
									formType === "enquiry") && (
									<FormField
										control={form.control}
										name="location_ids"
										render={({
											field: { value, onChange },
										}) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Locations{" "}
												</FormLabel>
												<MultiSelectDropdown
													value={value || []}
													onValueChange={(value) =>
														onChange(value)
													}
													placeholder="Select Locations"
													label="Select Locations"
													options={
														locationsData?.map(
															(location) => ({
																value: location.id.toString(),
																label: location.name,
															})
														) || []
													}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								{(formType === "service" ||
									formType === "feedback" ||
									formType === "enquiry") && (
									<FormField
										control={form.control}
										name="station_ids"
										render={({
											field: { value, onChange },
										}) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Providers{" "}
												</FormLabel>
												<MultiSelectDropdown
													value={value || []}
													onValueChange={(value) =>
														onChange(value)
													}
													placeholder="Select Providers"
													label="Select Providers"
													options={
														stationsData?.data?.map(
															(station) => ({
																value: station.id.toString(),
																label: station.name,
															})
														) || []
													}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								{formType === "referral" && (
									<FormField
										control={form.control}
										name="apply_to"
										render={({
											field: { value, onChange },
										}) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Apply To{" "}
												</FormLabel>
												<MultiSelectDropdown
													value={value || []}
													onValueChange={(value) =>
														onChange(value)
													}
													placeholder="Select Providers"
													label="Select Providers"
													options={
														stationsData?.data?.map(
															(station) => ({
																value: station.id.toString(),
																label: station.name,
															})
														) || []
													}
												/>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}
							</div>

							<div className="scrollbar-hide mt-6 max-h-[calc(100vh-129px)] w-full flex-1 overflow-y-auto pb-10">
								<div className="mx-auto w-full max-w-[827px] min-w-[700px] space-y-3.5 pr-28">
									<div className="flex items-center justify-end pb-1">
										<div className="flex items-center gap-2">
											<Button
												variant="outline"
												type="button"
												className="bg-primary hover:bg-primary/90 h-9 w-32.5 cursor-pointer text-xs font-medium text-white hover:text-white"
												onClick={handlePreview}
											>
												<ScanEye className="!h-3 !w-3" />
												Preview
											</Button>
										</div>
									</div>
									<Card className="pt-3 pb-7">
										<CardContent className="space-y-4 px-3">
											<FormField
												control={form.control}
												name="name"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium">
															Form Name{" "}
															<span className="text-red-500">
																*
															</span>
														</FormLabel>
														<FormControl>
															<Input
																placeholder="Form Name"
																className="text-xs placeholder:text-xs"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</CardContent>
									</Card>
									<Card className="pt-3 pb-7">
										<CardContent className="space-y-4 px-3">
											<FormField
												control={form.control}
												name="banner_url"
												render={({
													field: {
														value,
														onChange,
														...field
													},
												}) => (
													<FormItem>
														<FormLabel className="text-sm font-medium">
															Upload Banner
														</FormLabel>
														<FormControl>
															<div className="flex flex-col gap-2">
																<Uploader
																	files={[]}
																	onFilesChange={() => {}}
																	onFileRemove={() => {
																		onChange(
																			""
																		);
																	}}
																	descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
																	accept=".svg,.png,.jpg,.jpeg"
																	maxFileSize={
																		10 *
																		1024 *
																		1024
																	}
																	multiple={
																		false
																	}
																	maxFiles={1}
																	size="sm"
																	uploadText="Click or drag file here to upload file"
																	uploadIcon={
																		<Upload className="h-4 w-4 text-black" />
																	}
																	enableServerUpload={
																		true
																	}
																	onUploadSuccess={(
																		_,
																		url: string
																	) => {
																		onChange(
																			url
																		);
																	}}
																/>
															</div>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</CardContent>
									</Card>
									<Card className="pt-3 pb-7">
										<CardContent className="space-y-4 px-3">
											<FormField
												control={form.control}
												name="logo_url"
												render={({
													field: {
														value,
														onChange,
														...field
													},
												}) => (
													<FormItem>
														<FormLabel className="text-sm font-medium">
															Upload Logo
														</FormLabel>
														<FormControl>
															<div className="flex flex-col gap-2">
																<Uploader
																	files={[]}
																	onFilesChange={() => {}}
																	onFileRemove={() => {
																		onChange(
																			""
																		);
																	}}
																	descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
																	accept=".svg,.png,.jpg,.jpeg"
																	maxFileSize={
																		10 *
																		1024 *
																		1024
																	}
																	multiple={
																		false
																	}
																	maxFiles={1}
																	size="sm"
																	uploadText="Click or drag file here to upload file"
																	uploadIcon={
																		<Upload className="h-4 w-4 text-black" />
																	}
																	enableServerUpload={
																		true
																	}
																	onUploadSuccess={(
																		_,
																		url: string
																	) => {
																		onChange(
																			url
																		);
																	}}
																/>
															</div>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</CardContent>
									</Card>

									{sections.map((section, sectionIndex) => {
										const fields = form.watch(
											`sections.${sectionIndex}.fields`
										);
										const hasMultipleFields =
											fields?.length > 1;
										const lastFieldIndex =
											fields?.length - 1;
										const sortableFields = fields.map(
											(field) => field.id
										);

										return (
											<div
												key={section?.id}
												className="relative space-y-4"
												onMouseEnter={() =>
													setActiveSection(
														section?.id
													)
												}
											>
												<Card className="border-t-primary border-t-4 py-0">
													<CardContent className="space-y-6 p-4">
														<div className="flex items-center justify-between">
															<h2 className="text-base font-semibold text-slate-900">
																Section{" "}
																{sectionIndex +
																	1}
															</h2>
															{sections.length >
																1 &&
																(formType !==
																	"intake" ||
																	(formType ===
																		"intake" &&
																		sectionIndex >
																			0)) && (
																	<Button
																		type="button"
																		className="h-8 w-8 cursor-pointer"
																		variant="outline"
																		onClick={() =>
																			handleDeleteSection(
																				section?.id
																			)
																		}
																	>
																		<Trash2 className="h-4 w-4 text-red-500" />
																	</Button>
																)}
														</div>
														<div>
															<FormField
																control={
																	form.control
																}
																name={`sections.${sectionIndex}.title`}
																render={({
																	field,
																}) => (
																	<FormItem>
																		<FormLabel className="text-sm font-medium">
																			Section
																			Title{" "}
																			<span className="text-red-500">
																				*
																			</span>
																		</FormLabel>
																		<FormControl>
																			<Input
																				placeholder={`Section ${sectionIndex + 1} Title`}
																				className="text-xs placeholder:text-xs"
																				{...field}
																			/>
																		</FormControl>
																		<FormMessage />
																	</FormItem>
																)}
															/>
															{!descriptionVisibility[
																section.id
															] && (
																<button
																	type="button"
																	className="mt-4 ml-2.5 flex cursor-pointer items-center gap-3 text-xs font-normal text-[#060D25]"
																	onClick={() =>
																		handleToggleDescription(
																			section.id
																		)
																	}
																>
																	<svg
																		width="14"
																		height="14"
																		viewBox="0 0 14 14"
																		fill="none"
																		xmlns="http://www.w3.org/2000/svg"
																	>
																		<path
																			d="M4.6665 7.00033H9.33317M6.99984 4.66699V9.33366M12.8332 7.00033C12.8332 10.222 10.2215 12.8337 6.99984 12.8337C3.77818 12.8337 1.1665 10.222 1.1665 7.00033C1.1665 3.77866 3.77818 1.16699 6.99984 1.16699C10.2215 1.16699 12.8332 3.77866 12.8332 7.00033Z"
																			stroke="#005893"
																			strokeWidth="1.5"
																			strokeLinecap="round"
																			strokeLinejoin="round"
																		/>
																	</svg>
																	<span className="leading-none">
																		Add
																		Description
																	</span>
																</button>
															)}
															{descriptionVisibility[
																section.id
															] && (
																<FormField
																	control={
																		form.control
																	}
																	name={`sections.${sectionIndex}.description`}
																	render={({
																		field,
																	}) => (
																		<FormItem className="mt-4">
																			<FormLabel className="text-sm">
																				Description
																			</FormLabel>
																			<FormControl>
																				<Textarea
																					{...field}
																					placeholder="Add a description for this field"
																					value={
																						field.value ||
																						""
																					}
																				/>
																			</FormControl>
																			<FormMessage />
																		</FormItem>
																	)}
																/>
															)}
															{descriptionVisibility[
																section.id
															] && (
																<button
																	type="button"
																	className="mt-2 ml-2.5 flex cursor-pointer items-center gap-2 text-xs leading-none font-normal text-[#060D25]"
																	onClick={() =>
																		handleToggleDescription(
																			section.id
																		)
																	}
																>
																	<LuMinus
																		color="#fff"
																		className="rounded-full bg-red-600"
																		size={
																			12
																		}
																	/>
																	Hide
																	Description
																</button>
															)}
														</div>
													</CardContent>
												</Card>

												<SortableContext
													items={sortableFields}
													strategy={
														verticalListSortingStrategy
													}
												>
													{form
														.watch(
															`sections.${sectionIndex}.fields`
														)
														?.map(
															(
																field,
																fieldIndex
															) => (
																<FormField
																	key={
																		field.id
																	}
																	control={
																		form.control
																	}
																	name={`sections.${sectionIndex}.fields.${fieldIndex}`}
																	render={({
																		field: fieldProps,
																	}) => (
																		<DraggableFormField
																			sectionIndex={
																				sectionIndex
																			}
																			fieldIndex={
																				fieldIndex
																			}
																			field={{
																				...fieldProps,
																				name: fieldIndex,
																			}}
																			control={
																				form.control
																			}
																			watch={
																				form.watch
																			}
																			setValue={
																				form.setValue
																			}
																			getValues={
																				form.getValues
																			}
																			clearErrors={
																				form.clearErrors
																			}
																			moveField={
																				moveField
																			}
																			hasMultipleFields={
																				hasMultipleFields
																			}
																			isLastField={
																				fieldIndex ===
																				lastFieldIndex
																			}
																			activeSection={
																				activeSection
																			}
																			onAddField={
																				addField
																			}
																			onAddSection={() =>
																				addSection(
																					sectionIndex
																				)
																			}
																			sectionId={
																				section.id
																			}
																			form={
																				form
																			}
																		/>
																	)}
																/>
															)
														)}
												</SortableContext>

												{!hasMultipleFields && (
													<FieldControl
														sectionIndex={
															sectionIndex
														}
														onAddField={addField}
														onAddSection={() =>
															addSection(
																sectionIndex
															)
														}
														isVisible={
															activeSection ===
															section?.id
														}
														position="section"
													/>
												)}

												<div className="mt-4">
													<FormFlowSelect
														control={form.control}
														sectionIndex={
															sectionIndex
														}
														sections={form.getValues(
															"sections"
														)}
														currentValue={form.watch(
															`sections.${sectionIndex}.flow`
														)}
														onValueChange={(
															value: any
														) => {
															form.setValue(
																`sections.${sectionIndex}.flow`,
																value,
																{
																	shouldValidate:
																		true,
																}
															);
														}}
													/>
												</div>
											</div>
										);
									})}

									<Card className="p-0">
										<CardContent className="space-y-4 p-4">
											<h2 className="!mb-2 text-base leading-6 font-semibold">
												Submission{" "}
											</h2>
											<div className="rounded-md border border-gray-200 p-4 pt-4">
												<FormField
													control={form.control}
													name="success_message"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-normal italic">
																Success Message
																on Submission{" "}
																<span className="text-red-500">
																	*
																</span>
															</FormLabel>
															<FormControl>
																<Input
																	placeholder={`"Enter here"`}
																	className="bg-gray-100 shadow-sm"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>
											<div className="rounded-md border border-gray-200 p-4 pt-4">
												<FormField
													control={form.control}
													name="block_message"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-normal italic">
																Block Message on
																Submission{" "}
															</FormLabel>
															<FormControl>
																<Input
																	placeholder={`"Enter here"`}
																	className="bg-gray-100 shadow-sm"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>
											<div className="border-t border-gray-200 pt-2">
												<FormField
													control={form.control}
													name="submit_button_title"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-normal italic">
																Submission
																Button Title{" "}
																<span className="text-red-500">
																	*
																</span>
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Button title"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>
										</CardContent>
									</Card>

									<div className="flex items-center justify-end space-x-2 pt-5">
										<Button
											type="submit"
											variant={"outline"}
											className="cursor-pointer"
											onClick={() =>
												form.setValue("status", "live")
											}
											disabled={isCreatingForm}
										>
											{form.watch("status") === "live" &&
											isCreatingForm ? (
												<Loader2 className="h-4 w-4 animate-spin text-white" />
											) : (
												"Publish"
											)}
										</Button>
										<Button
											type="submit"
											className="cursor-pointer"
											onClick={() =>
												form.setValue("status", "draft")
											}
											disabled={isCreatingForm}
										>
											{form.watch("status") === "draft" &&
											isCreatingForm ? (
												<Loader2 className="h-4 w-4 animate-spin text-white" />
											) : (
												"Save as Draft"
											)}
										</Button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</Form>
		</DndContext>
	);
};
