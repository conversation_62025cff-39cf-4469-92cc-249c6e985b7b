import { apiClient } from "@/lib/api/clients";
import * as Types from "../types";

//**************************** Form API ****************************//

/**
 * Get Forms
 * @param params - Query parameters
 * @returns List of Forms
 */

export const APIVersion3GetForms = async (
	params: Types.FormTypes.GetFormsQueryParams,
	organizationId: number
): Promise<Types.FormTypes.GetFormsResponse> => {
	try {
		const response = await apiClient.get(`/api/v1/forms/`, {
			params,
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		});
		return response.data as Types.FormTypes.GetFormsResponse;
	} catch (error) {
		throw error;
	}
};

/**
 * Get Form
 * @param form_uuid - Form uuid
 * @param organizationId - Organization ID
 * @returns Form
 */

export const APIVersion3GetForm = async (
	form_uuid: string,
	organizationId: number
): Promise<Types.FormTypes.GetFormResponse> => {
	const response = await apiClient.get(`/api/v1/forms/${form_uuid}`, {
		headers: {
			"X-organizationId": organizationId.toString(),
		},
	});
	return response.data;
};

/**
 * Create Form
 * @param data - Form data
 * @returns Form created response
 */

export const APIVersion3CreateForm = async (
	data: Types.FormTypes.FormDataType,
	organizationId: number
): Promise<Types.FormTypes.CreateFormResponse> => {
	const response = await apiClient.post("/api/v1/forms/", data, {
		headers: {
			"X-organizationId": organizationId.toString(),
		},
	});
	return response.data;
};

/**
 * Update Form
 * @param form_uuid - Form uuid
 * @param data - Form data
 * @param organizationId - Organization ID
 * @returns Form updated response
 */

export const APIVersion3UpdateForm = async (
	form_uuid: string,
	data: Types.FormTypes.FormDataType,
	organizationId: number
): Promise<Types.FormTypes.UpdateFormResponse> => {
	const response = await apiClient.put(
		`/api/v1/forms/${form_uuid}`,
		{
			data,
		},
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Delete Form
 * @param form_uuid - Form uuid
 * @returns Form deleted response
 */

export const APIVersion3DeleteForm = async (
	form_uuid: string,
	organizationId: number
): Promise<Types.FormTypes.DeleteFormResponse> => {
	const response = await apiClient.delete(`/api/v1/forms/${form_uuid}`, {
		headers: {
			"X-organizationId": organizationId.toString(),
		},
	});
	return response.data;
};

//**************************** Form Responses API ****************************//

/**
 * Get Form Responses
 * @param params - Query parameters
 * @param organizationId - Organization ID
 * @returns List of Forms
 */

export const APIVersion3GetFormResponses = async (
	params: Types.FormResponseTypes.GetFormResponsesQueryParams,
	organizationId: number
): Promise<Types.FormResponseTypes.GetFormResponsesResponse> => {
	const response = await apiClient.get(`/api/v1/form-responses/`, {
		params,
		headers: {
			"X-organizationId": organizationId.toString(),
		},
	});
	return response.data;
};

/**
 * Get Form Response
 * @param response_uuid - Form response uuid
 * @param organizationId - Organization ID
 * @returns Form response
 */

export const APIVersion3GetFormResponse = async (
	response_uuid: string,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseResponseData> => {
	const response = await apiClient.get(
		`/api/v1/form-responses/${response_uuid}`,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Create Form Responses
 * @param data - Form responses data
 * @param organizationId - Organization ID
 * @returns Form responses created response
 */

export const APIVersion3CreateFormResponses = async (
	data: Types.FormResponseTypes.CreateFormResponses,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseResponseData> => {
	const response = await apiClient.post("/api/v1/form-responses/", {
		data,
		headers: {
			"X-organizationId": organizationId.toString(),
		},
	});
	return response.data;
};

/**
 * Update Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response data
 * @param organizationId - Organization ID
 * @returns Form response updated response
 */

export const APIVersion3UpdateFormResponse = async (
	response_uuid: string,
	data: Types.FormResponseTypes.UpdateFormResponsePayload,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseResponseData> => {
	const response = await apiClient.put(
		`/api/v1/form-responses/${response_uuid}`,
		{
			data,
		},
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Delete Form Response
 * @param response_uuid - Form response uuid
 * @param organizationId - Organization id
 * @returns Form response deleted response
 */

export const APIVersion3DeleteFormResponse = async (
	response_uuid: string,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseResponseData> => {
	const response = await apiClient.delete(
		`/api/v1/form-responses/${response_uuid}`,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Get Specific Form Responses
 * @param form_uuid - Form uuid
 * @param organizationId - Organization id
 * @returns Form responses
 */

export const APIVersion3GetSpecificFormResponses = async (
	form_uuid: string,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseResponseData> => {
	const response = await apiClient.get(
		`/api/v1/forms/${form_uuid}/responses`,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

//**************************** Form Responses Mark Type API ****************************//

/**
 * Get Form Responses Mark Type
 * @param organizationId - Organization id
 * @returns Form responses mark type
 */

export const APIVersion3GetFormResponsesMarkType = async (
	organizationId: number
): Promise<Types.FormResponseTypes.GetFormResponsesMarkTypeResponse> => {
	const response = await apiClient.get(`/api/v1/form-responses/mark-types`, {
		headers: {
			"X-organizationId": organizationId.toString(),
		},
	});
	return response.data;
};

/**
 * Mark Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response mark type
 * @returns Form response marked response
 */

export const APIVersion3MarkFormResponse = async (
	response_uuid: string,
	data: Types.FormResponseTypes.MarkFormResponsePayload,
	organizationId: number
): Promise<Types.FormResponseTypes.CreateMarkFormResponseResponse> => {
	const response = await apiClient.post(
		`/api/v1/form-responses/${response_uuid}/mark`,
		data,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Approve Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response approval payload
 * @returns Form response approved response
 */

export const APIVersion3ApproveFormResponse = async (
	response_uuid: string,
	data: Types.FormResponseTypes.FormResponseApprovalPayload,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseApprovalResponse> => {
	const response = await apiClient.post(
		`/api/v1/form-responses/${response_uuid}/approve`,
		data,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Reject Form Response
 * @param response_uuid - Form response uuid
 * @param data - Form response approval payload
 * @returns Form response rejected response
 */

export const APIVersion3RejectFormResponse = async (
	response_uuid: string,
	data: Types.FormResponseTypes.FormResponseApprovalPayload,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseApprovalResponse> => {
	const response = await apiClient.post(
		`/api/v1/form-responses/${response_uuid}/reject`,
		data,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};

/**
 * Delete Form Response Mark
 * @param response_uuid - Form response uuid
 * @param organizationId - Organization id
 * @returns Form response mark deleted response
 */

export const APIVersion3DeleteFormResponseMark = async (
	response_uuid: string,
	organizationId: number
): Promise<Types.FormResponseTypes.FormResponseResponseData> => {
	const response = await apiClient.delete(
		`/api/v1/form-responses/${response_uuid}/mark`,
		{
			headers: {
				"X-organizationId": organizationId.toString(),
			},
		}
	);
	return response.data;
};
